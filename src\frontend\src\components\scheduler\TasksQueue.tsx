import React, { useState, useCallback, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { useToast } from '@/components/ui/use-toast';
import {
  ChevronDown,
  ChevronRight,
  Calendar,
  Zap,
  XCircle,
  CheckCircle,
  RefreshCw,
  Activity,
  Clock,
  AlertTriangle,
  Timer,
  Database,
  User,
  ExternalLink,
  GripVertical,
  Play,
  Pause,
  RotateCcw,
  Trash2,
  Edit,
  FastForward
} from 'lucide-react';

import type {
  PlannedTask,
  ActiveTask,
  FailedTask,
  CompletedTask
} from '@/types/scheduler';
import type { RealTimeProgressUpdate } from '@/types/scanLogs';
import type { Progress as ProgressType } from '@/types/scanLogs';

import {
  TaskTypeColors,
  formatRelativeTime,
  getTaskTypeDisplayName
} from '@/types/scheduler';

import { CompactProgress } from '@/components/ui/enhanced-progress';

interface TasksQueueProps {
  // Task data
  plannedTasks: PlannedTask[];
  activeTasks: ActiveTask[];
  failedTasks: FailedTask[];
  completedTasks: CompletedTask[];

  // Loading states
  loading?: boolean;

  // WebSocket data for real-time progress
  isConnected?: boolean;
  isConnecting?: boolean;
  wsError?: string | null;
  lastUpdate?: RealTimeProgressUpdate | null;
  isAdminSubscribed?: boolean;
  realTimeProgress?: Map<string, ProgressType>;
  globalScans?: Map<string, any>;

  // Event handlers
  onRefresh?: () => void;
  onTriggerTask?: (taskId: string) => void;
  onDelayTask?: (taskId: string, delayMinutes: number) => void;
  onPauseTask?: (taskId: string, taskType?: string) => void;
  onResumeTask?: (taskId: string, taskType?: string) => void;
  onCancelTask?: (taskId: string, taskType?: string) => void;
  onRetryTask?: (taskId: string) => void;
  onDeleteTask?: (taskId: string) => void;
  onEditTask?: (taskId: string, updates: Partial<PlannedTask>) => void;
  onReorderTasks?: (taskIds: string[]) => void;

  // Real-time monitoring controls
  enableRealTimeMonitoring?: boolean;
  onToggleRealTimeMonitoring?: () => void;
  onConnect?: () => void;

  // Retry fix - tracking recently retried tasks
  recentlyRetriedTasks?: Set<string>;
  onTaskRestarted?: (taskId: string, taskType: string) => void;
}

// Default open states for swimlanes
const DEFAULT_SWIMLANE_STATES = {
  active: true,      // Active tasks expanded by default
  scheduled: true,   // Scheduled tasks expanded by default
  failed: true,      // Failed tasks expanded by default
  completed: false   // Completed tasks collapsed by default
};

const TasksQueue: React.FC<TasksQueueProps> = ({
  plannedTasks = [],
  activeTasks = [],
  failedTasks = [],
  completedTasks = [],
  loading = false,
  isConnected = false,
  isConnecting = false,
  wsError = null,
  lastUpdate = null,
  isAdminSubscribed = false,
  realTimeProgress = new Map(),
  globalScans = new Map(),
  onRefresh,
  onTriggerTask,
  onDelayTask,
  onPauseTask,
  onResumeTask,
  onCancelTask,
  onRetryTask,
  onDeleteTask,
  onEditTask,
  onReorderTasks,
  enableRealTimeMonitoring = true,
  onToggleRealTimeMonitoring,
  onConnect,
  recentlyRetriedTasks = new Set(),
  onTaskRestarted
}) => {
  const { toast } = useToast();

  // Drag and drop state for scheduled tasks
  const [draggedTask, setDraggedTask] = useState<string | null>(null);
  const [dragOverTask, setDragOverTask] = useState<string | null>(null);

  // Action states
  const [actioningTasks, setActioningTasks] = useState<Set<string>>(new Set());
  const [expandedTasks, setExpandedTasks] = useState<Set<string>>(new Set());

  // Auto-refresh functionality with last refresh tracking
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  useEffect(() => {
    if (!onRefresh) return;

    // Set up auto-refresh every 20 seconds
    const interval = setInterval(() => {
      console.log('TasksQueue: Auto-refreshing data...');
      onRefresh();
      setLastRefresh(new Date());
    }, 20000);

    return () => clearInterval(interval);
  }, [onRefresh]);

  // Update last refresh when manual refresh is triggered
  useEffect(() => {
    setLastRefresh(new Date());
  }, [plannedTasks, activeTasks, failedTasks, completedTasks]);

  // Helper function to detect scheduler events
  const isSchedulerEvent = (update: any): boolean => {
    const schedulerEventTypes = [
      'task_cancelled',
      'task_restarted',
      'task_removed',
      'task_paused',
      'task_resumed',
      'scheduler_refresh'
    ];
    return schedulerEventTypes.includes(update?.eventType);
  };

  // Handle WebSocket events for task restarts
  useEffect(() => {
    console.log('TasksQueue: WebSocket update received:', lastUpdate);

    // Only process scheduler events, not scan progress updates
    if (lastUpdate && isSchedulerEvent(lastUpdate)) {
      console.log('TasksQueue: Scheduler event detected:', lastUpdate.eventType);

      if (lastUpdate.eventType === 'task_restarted') {
        console.log('TasksQueue: task_restarted event detected:', {
          success: lastUpdate.success,
          taskId: lastUpdate.taskId,
          taskType: lastUpdate.taskType,
          onTaskRestarted: !!onTaskRestarted
        });
        if (lastUpdate.success && lastUpdate.taskId && onTaskRestarted) {
          console.log('TasksQueue: Calling onTaskRestarted for task:', lastUpdate.taskId);
          onTaskRestarted(lastUpdate.taskId, lastUpdate.taskType || 'usage_scan');
        }
      }
    }
  }, [lastUpdate, onTaskRestarted]);

  // Drag and drop handlers for scheduled tasks
  const handleDragStart = useCallback((e: React.DragEvent, taskId: string) => {
    setDraggedTask(taskId);
    e.dataTransfer.effectAllowed = 'move';
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent, taskId: string) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    setDragOverTask(taskId);
  }, []);

  const handleDragLeave = useCallback(() => {
    setDragOverTask(null);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent, targetTaskId: string) => {
    e.preventDefault();

    if (!draggedTask || draggedTask === targetTaskId) {
      setDraggedTask(null);
      setDragOverTask(null);
      return;
    }

    // Find the tasks and reorder them
    const draggedIndex = plannedTasks.findIndex(task => task.id === draggedTask);
    const targetIndex = plannedTasks.findIndex(task => task.id === targetTaskId);

    if (draggedIndex === -1 || targetIndex === -1) {
      setDraggedTask(null);
      setDragOverTask(null);
      return;
    }

    // Create new order
    const newOrder = [...plannedTasks];
    const [movedTask] = newOrder.splice(draggedIndex, 1);
    newOrder.splice(targetIndex, 0, movedTask);

    // Extract task IDs in new order
    const newTaskIds = newOrder.map(task => task.id);

    // Call the reorder handler
    if (onReorderTasks) {
      onReorderTasks(newTaskIds);
    }

    setDraggedTask(null);
    setDragOverTask(null);
  }, [draggedTask, plannedTasks, onReorderTasks]);

  // Task action handlers with immediate refresh
  const handleTaskAction = useCallback(async (taskId: string, action: string, taskType?: string) => {
    setActioningTasks(prev => new Set(prev).add(taskId));

    try {
      let success = false;
      let message = '';

      switch (action) {
        case 'trigger':
          if (onTriggerTask) {
            await onTriggerTask(taskId);
            success = true;
            message = 'Task triggered successfully';
          }
          break;
        case 'pause':
          if (onPauseTask) {
            await onPauseTask(taskId, taskType);
            success = true;
            message = 'Task paused successfully';
          }
          break;
        case 'resume':
          if (onResumeTask) {
            await onResumeTask(taskId, taskType);
            success = true;
            message = 'Task resumed successfully';
          }
          break;
        case 'cancel':
          if (onCancelTask) {
            await onCancelTask(taskId, taskType);
            success = true;
            message = 'Task cancelled successfully';
          }
          break;
        case 'retry':
          if (onRetryTask) {
            console.log(`TasksQueue: Retrying task ${taskId}`);
            await onRetryTask(taskId);
            success = true;
            message = 'Task retry initiated successfully, updating UI';
            console.log('TasksQueue: Task retry initiated successfully, updating UI');
          }
          break;
        case 'delete':
          if (onDeleteTask) {
            await onDeleteTask(taskId);
            success = true;
            message = 'Task deleted successfully';
          }
          break;
        default:
          console.warn(`Unknown action: ${action}`);
          throw new Error(`Unknown action: ${action}`);
      }

      if (success) {
        // Show success toast
        toast({
          title: 'Success',
          description: message,
        });

        // Immediate refresh to update UI state
        if (onRefresh) {
          console.log(`TasksQueue: Refreshing after ${action} action on task ${taskId}`);
          // Use longer delay for retry actions to allow backend processing and init phase
          const delay = action === 'retry' ? 2000 : 500;
          setTimeout(() => onRefresh(), delay);
        }
      }
    } catch (error) {
      console.error(`Failed to ${action} task:`, error);
      toast({
        title: 'Error',
        description: `Failed to ${action} task`,
        variant: 'destructive',
      });
    } finally {
      setActioningTasks(prev => {
        const newSet = new Set(prev);
        newSet.delete(taskId);
        return newSet;
      });
    }
  }, [onTriggerTask, onPauseTask, onResumeTask, onCancelTask, onRetryTask, onDeleteTask, onRefresh, toast]);

  // Toggle task details expansion
  const toggleTaskExpansion = useCallback((taskId: string) => {
    setExpandedTasks(prev => {
      const newSet = new Set(prev);
      if (newSet.has(taskId)) {
        newSet.delete(taskId);
      } else {
        newSet.add(taskId);
      }
      return newSet;
    });
  }, []);

  // Manual refresh handler
  const handleManualRefresh = useCallback(() => {
    if (onRefresh) {
      console.log('TasksQueue: Manual refresh triggered');
      onRefresh();
      setLastRefresh(new Date());
    }
  }, [onRefresh]);

  // Create test jobs handler (for development)
  const handleCreateTestJobs = useCallback(async () => {
    try {
      const response = await fetch('/api/scheduler/control/create-test-jobs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();

      if (result.success) {
        toast({
          title: 'Success',
          description: result.message || 'Test jobs created successfully',
        });

        // Refresh data to show new test jobs
        if (onRefresh) {
          setTimeout(() => onRefresh(), 1000);
        }
      } else {
        toast({
          title: 'Error',
          description: result.message || 'Failed to create test jobs',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Failed to create test jobs:', error);
      toast({
        title: 'Error',
        description: 'Failed to create test jobs',
        variant: 'destructive',
      });
    }
  }, [onRefresh, toast]);

  // Get task progress for active tasks
  const getTaskProgress = useCallback((task: ActiveTask): ProgressType | undefined => {
    if (task.type === 'usage_scan') {
      // For usage scans, check if we have progress data by task ID first
      const taskProgress = realTimeProgress.get(task.id);
      if (taskProgress) {
        return taskProgress;
      }

      // Check for scan IDs that match this task pattern (GroupName:RepoID:timestamp:active)
      const taskIdPattern = `${task.id}:`;
      for (const [key, progress] of realTimeProgress.entries()) {
        if (key.startsWith(taskIdPattern) && key.includes(':active')) {
          return progress;
        }
      }

      // Fallback to scan key format for backward compatibility
      const scanKey = `${task.repository}-${task.groupName}`;
      const scanProgress = realTimeProgress.get(scanKey);
      if (scanProgress) {
        return scanProgress;
      }
    } else if (task.type === 'scan' || task.type === 'auto-scan') {
      // For other scan types, match by repository and group name
      const scanKey = `${task.repository}-${task.groupName}`;
      const scanProgress = realTimeProgress.get(scanKey);
      if (scanProgress) {
        return scanProgress;
      }
    }

    return undefined;
  }, [realTimeProgress]);

  // Get active scans from WebSocket that don't have corresponding active tasks
  const activeWebSocketScans = Array.from(globalScans?.values() || []).filter(scan => {
    try {
      if (!scan || scan.status !== 'running') return false;

      // Filter out scans with missing essential data
      if (!scan.groupName || !scan.repoId) {
        return false;
      }

      // Check if there's already an active task for this scan
      const hasActiveTask = activeTasks?.some(task =>
        task?.type === 'usage_scan' &&
        task?.repository === scan.repoId &&
        task?.groupName === scan.groupName &&
        task?.status === 'running'
      );

      // Only include scans that don't have corresponding active tasks
      return !hasActiveTask;
    } catch (error) {
      console.error('Error filtering WebSocket scan:', error, scan);
      return false;
    }
  });

  const totalActiveItems = (activeTasks?.length || 0) + activeWebSocketScans.length;
  const totalPendingTasks = (plannedTasks?.length || 0) + totalActiveItems + (failedTasks?.length || 0);

  const formatDateTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleString();
    } catch {
      return dateString;
    }
  };

  const formatDuration = (durationMs: number) => {
    try {
      if (!durationMs || durationMs < 1000) {
        return `${durationMs || 0}ms`;
      }
      const seconds = Math.floor(durationMs / 1000);
      if (seconds < 60) {
        return `${seconds}s`;
      }
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes}m ${remainingSeconds}s`;
    } catch (error) {
      console.error('Error formatting duration:', error);
      return '0s';
    }
  };

  // Safe date formatting helper
  const safeFormatRelativeTime = (dateInput: string | Date | undefined | null) => {
    try {
      if (!dateInput) return 'Unknown time';

      let dateString: string;
      if (typeof dateInput === 'string') {
        dateString = dateInput;
      } else if (dateInput instanceof Date) {
        dateString = dateInput.toISOString();
      } else {
        return 'Unknown time';
      }

      return formatRelativeTime(dateString);
    } catch (error) {
      console.error('Error formatting relative time:', error, dateInput);
      return 'Unknown time';
    }
  };

  // Error boundary wrapper
  try {
    return (
      <Card>
        <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Timer className="h-5 w-5 text-blue-600" />
              <span>Tasks Queue</span>
              <Badge variant="secondary">
                {totalPendingTasks} pending
              </Badge>
            </CardTitle>
            <CardDescription>
              Unified view of all scheduled, active, failed, and completed tasks with real-time progress monitoring
              <br />
              <span className="text-xs text-gray-500">
                Last updated: {lastRefresh.toLocaleTimeString()} • Auto-refresh every 20s
              </span>
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            {/* Real-time monitoring toggle */}
            {onToggleRealTimeMonitoring && (
              <Button
                variant="outline"
                size="sm"
                onClick={onToggleRealTimeMonitoring}
                className={enableRealTimeMonitoring ? 'bg-green-50 border-green-200 text-green-700' : ''}
              >
                <Activity className={`h-4 w-4 mr-2 ${enableRealTimeMonitoring ? 'text-green-600' : ''}`} />
                {enableRealTimeMonitoring ? 'Real-time On' : 'Real-time Off'}
              </Button>
            )}

            {/* Development: Create test jobs button */}
            {process.env.NODE_ENV === 'development' && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleCreateTestJobs}
                disabled={loading}
                className="text-blue-600 border-blue-300 hover:bg-blue-50"
              >
                <Zap className="h-4 w-4 mr-2" />
                Create Test Jobs
              </Button>
            )}

            {onRefresh && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleManualRefresh}
                disabled={loading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                {loading ? 'Refreshing...' : 'Refresh'}
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Active Tasks Swimlane */}
        <Collapsible defaultOpen={DEFAULT_SWIMLANE_STATES.active}>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" className="w-full justify-between p-4 h-auto">
              <div className="flex items-center space-x-3">
                <Zap className="h-5 w-5 text-green-600" />
                <span className="font-semibold">Active Tasks</span>
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  {totalActiveItems} running
                </Badge>
              </div>
              <ChevronRight className="h-4 w-4 transition-transform group-data-[state=open]:rotate-90" />
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-3 mt-2">
            {totalActiveItems === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Zap className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p>No active tasks running</p>
              </div>
            ) : (
              <div className="space-y-3">
                {/* Active Tasks from Scheduler */}
                {(activeTasks || []).map((task) => {
                  const taskProgress = getTaskProgress(task);
                  const typeColor = TaskTypeColors[task.type] || TaskTypeColors.default;
                  const isExpanded = expandedTasks.has(task.id);
                  const isActioning = actioningTasks.has(task.id);

                  return (
                    <div key={task.id} className="border rounded-lg p-4 bg-white">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <Badge className={`${typeColor.bg} ${typeColor.text} ${typeColor.border}`}>
                            {getTaskTypeDisplayName(task.type)}
                          </Badge>
                          {task.status === 'paused' && (
                            <Badge variant="outline" className="text-orange-600 border-orange-300 bg-orange-50">
                              <Pause className="h-3 w-3 mr-1" />
                              Paused
                            </Badge>
                          )}
                          {task.status === 'completing' && (
                            <Badge variant="outline" className="text-blue-600 border-blue-300 bg-blue-50">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Completing
                            </Badge>
                          )}
                          <div>
                            <h4 className="font-medium text-gray-900">{task.name}</h4>
                            <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                              <div className="flex items-center space-x-1">
                                <Database className="h-3 w-3" />
                                <span>{task.repository}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <User className="h-3 w-3" />
                                <span>{task.groupName}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <Clock className="h-3 w-3" />
                                <span>Started {safeFormatRelativeTime(task.startedAt)}</span>
                              </div>
                              {task.workerId && (
                                <div className="flex items-center space-x-1">
                                  <span className="text-xs bg-gray-100 px-2 py-1 rounded">
                                    Worker: {task.workerId}
                                  </span>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {task.status === 'running' ? (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleTaskAction(task.id, 'pause', task.type)}
                              disabled={isActioning}
                              title="Pause Task"
                            >
                              <Pause className="h-3 w-3 mr-1" />
                              Pause
                            </Button>
                          ) : (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleTaskAction(task.id, 'resume', task.type)}
                              disabled={isActioning}
                              title="Resume Task"
                            >
                              <Play className="h-3 w-3 mr-1" />
                              Resume
                            </Button>
                          )}
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleTaskAction(task.id, 'cancel', task.type)}
                            disabled={isActioning}
                            className="text-red-600 hover:text-red-700"
                            title="Cancel Task"
                          >
                            <XCircle className="h-3 w-3 mr-1" />
                            Cancel
                          </Button>
                        </div>
                      </div>

                      {/* Real-time Progress Bar */}
                      {(task.progress || taskProgress) && (
                        <div className="mb-3">
                          {taskProgress && enableRealTimeMonitoring ? (
                            <div className="space-y-2">
                              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                <Activity className="h-3 w-3 animate-pulse text-green-500" />
                                <span>Live Progress</span>
                                {isConnected && (
                                  <span className="text-green-600 font-medium">● Connected</span>
                                )}
                              </div>
                              <CompactProgress
                                progress={taskProgress}
                                className="mt-1"
                                showCurrentFile={true}
                              />
                            </div>
                          ) : task.progress ? (
                            <div>
                              <div className="flex items-center justify-between mb-2">
                                <span className="text-sm font-medium text-gray-700">
                                  {task.progress.description || 'Processing...'}
                                </span>
                                <span className="text-sm text-gray-500">
                                  {task.progress.current}/{task.progress.total} ({task.progress.percentage}%)
                                </span>
                              </div>
                              <div className="w-full bg-gray-200 rounded-full h-2">
                                <div
                                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                  style={{ width: `${task.progress.percentage}%` }}
                                />
                              </div>
                            </div>
                          ) : (
                            <div className="text-center py-2 text-gray-500">
                              <Activity className="h-4 w-4 animate-spin mx-auto mb-1" />
                              <p className="text-xs">Initializing...</p>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  );
                })}

                {/* Active WebSocket Scans */}
                {activeWebSocketScans.map((scan) => (
                  <div
                    key={`${scan.repoId}-${scan.groupName}`}
                    className="border rounded-lg p-4 bg-blue-50/50 border-blue-200"
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <Badge className="bg-blue-100 text-blue-800 border-blue-200">
                          Live Scan
                        </Badge>
                        <div>
                          <h4 className="font-medium text-gray-900">Usage Scan: {scan.groupName}</h4>
                          <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                            <div className="flex items-center space-x-1">
                              <Database className="h-3 w-3" />
                              <span>{scan.repoId}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <User className="h-3 w-3" />
                              <span>{scan.groupName}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Clock className="h-3 w-3" />
                              <span>Started {safeFormatRelativeTime(scan.startTime)}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Live Progress */}
                    {scan.progress && scan.status === 'running' && (
                      <div className="mb-3">
                        <div className="space-y-2">
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <Activity className="h-3 w-3 animate-pulse text-green-500" />
                            <span>Live Progress</span>
                          </div>
                          <CompactProgress
                            progress={scan.progress}
                            className="mt-1"
                            showCurrentFile={true}
                          />
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </CollapsibleContent>
        </Collapsible>

        {/* Scheduled Tasks Swimlane */}
        <Collapsible defaultOpen={DEFAULT_SWIMLANE_STATES.scheduled}>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" className="w-full justify-between p-4 h-auto">
              <div className="flex items-center space-x-3">
                <Calendar className="h-5 w-5 text-blue-600" />
                <span className="font-semibold">Scheduled Tasks</span>
                <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                  {plannedTasks?.length || 0} scheduled
                </Badge>
              </div>
              <ChevronRight className="h-4 w-4 transition-transform group-data-[state=open]:rotate-90" />
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-3 mt-2">
            {(plannedTasks?.length || 0) === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Calendar className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p>No scheduled tasks</p>
              </div>
            ) : (
              <div className="space-y-3">
                {(plannedTasks || []).map((task) => {
                  const typeColor = TaskTypeColors[task.type] || TaskTypeColors.default;
                  const isOverdue = new Date(task.scheduledFor) < new Date();
                  const isExpanded = expandedTasks.has(task.id);
                  const isActioning = actioningTasks.has(task.id);
                  const isDragging = draggedTask === task.id;
                  const isDragOver = dragOverTask === task.id;

                  return (
                    <div
                      key={task.id}
                      className={`border rounded-lg p-4 bg-white transition-all duration-200 ${
                        isDragging ? 'opacity-50 scale-95' : ''
                      } ${
                        isDragOver ? 'border-blue-400 bg-blue-50' : ''
                      }`}
                      draggable
                      onDragStart={(e) => handleDragStart(e, task.id)}
                      onDragOver={(e) => handleDragOver(e, task.id)}
                      onDragLeave={handleDragLeave}
                      onDrop={(e) => handleDrop(e, task.id)}
                    >
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <GripVertical className="h-4 w-4 text-gray-400 cursor-grab" />
                          <Badge className={`${typeColor.bg} ${typeColor.text} ${typeColor.border}`}>
                            {getTaskTypeDisplayName(task.type)}
                          </Badge>
                          {isOverdue && (
                            <Badge variant="destructive" className="text-xs">
                              <AlertTriangle className="h-3 w-3 mr-1" />
                              Overdue
                            </Badge>
                          )}
                          <div>
                            <h4 className="font-medium text-gray-900">{task.name}</h4>
                            <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                              <div className="flex items-center space-x-1">
                                <Database className="h-3 w-3" />
                                <span>{task.repository}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <User className="h-3 w-3" />
                                <span>{task.groupName}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <Timer className="h-3 w-3" />
                                <span className={isOverdue ? 'text-red-600 font-medium' : ''}>
                                  {safeFormatRelativeTime(task.scheduledFor)}
                                </span>
                              </div>
                              {task.createdAt && (
                                <div className="flex items-center space-x-1 text-xs text-gray-400">
                                  <span>Created {safeFormatRelativeTime(task.createdAt)}</span>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleTaskAction(task.id, 'trigger')}
                            disabled={isActioning}
                            title="Trigger Now"
                          >
                            {isActioning ? (
                              <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                            ) : (
                              <FastForward className="h-3 w-3 mr-1" />
                            )}
                            {isActioning ? 'Triggering...' : 'Trigger Now'}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => toggleTaskExpansion(task.id)}
                            title="More Actions"
                          >
                            <Edit className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>

                      {/* Expanded Actions */}
                      {isExpanded && (
                        <div className="mt-3 pt-3 border-t border-gray-200">
                          <div className="flex items-center gap-2 mb-3">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleTaskAction(task.id, 'cancel', task.type)}
                              disabled={isActioning}
                              className="text-red-600 hover:text-red-700"
                            >
                              <XCircle className="h-3 w-3 mr-1" />
                              Cancel
                            </Button>
                            {onEditTask && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => onEditTask(task.id, {})}
                                disabled={isActioning}
                              >
                                <Edit className="h-3 w-3 mr-1" />
                                Edit
                              </Button>
                            )}
                            {onDelayTask && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => onDelayTask(task.id, 30)}
                                disabled={isActioning}
                              >
                                <Clock className="h-3 w-3 mr-1" />
                                Delay 30min
                              </Button>
                            )}
                          </div>
                        </div>
                      )}

                      {task.description && (
                        <p className="text-sm text-gray-600 mt-2">{task.description}</p>
                      )}
                    </div>
                  );
                })}
              </div>
            )}
          </CollapsibleContent>
        </Collapsible>

        {/* Failed Tasks Swimlane */}
        <Collapsible defaultOpen={DEFAULT_SWIMLANE_STATES.failed}>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" className="w-full justify-between p-4 h-auto">
              <div className="flex items-center space-x-3">
                <XCircle className="h-5 w-5 text-red-600" />
                <span className="font-semibold">Failed Tasks</span>
                <Badge variant="destructive">
                  {failedTasks?.length || 0} failed
                </Badge>
              </div>
              <ChevronRight className="h-4 w-4 transition-transform group-data-[state=open]:rotate-90" />
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-3 mt-2">
            {(failedTasks?.length || 0) === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <CheckCircle className="h-8 w-8 text-green-400 mx-auto mb-2" />
                <p>No failed tasks - all systems running smoothly!</p>
              </div>
            ) : (
              <div className="space-y-3">
                {(failedTasks || [])
                  .filter(task => !recentlyRetriedTasks.has(task.id)) // Filter out recently retried tasks
                  .map((task) => {
                  const typeColor = TaskTypeColors[task.type] || TaskTypeColors.default;
                  const isExpanded = expandedTasks.has(task.id);
                  const isActioning = actioningTasks.has(task.id);

                  return (
                    <div key={task.id} className="border rounded-lg p-4 bg-red-50/50 border-red-200">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <Badge className={`${typeColor.bg} ${typeColor.text} ${typeColor.border}`}>
                            {getTaskTypeDisplayName(task.type)}
                          </Badge>
                          <div>
                            <h4 className="font-medium text-gray-900">{task.name}</h4>
                            <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                              <div className="flex items-center space-x-1">
                                <Database className="h-3 w-3" />
                                <span>{task.repository}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <User className="h-3 w-3" />
                                <span>{task.groupName}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <Clock className="h-3 w-3" />
                                <span>Failed {safeFormatRelativeTime(task.failedAt)}</span>
                              </div>
                              {task.retryCount > 0 && (
                                <div className="flex items-center space-x-1">
                                  <span className="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded">
                                    Retries: {task.retryCount}
                                  </span>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {task.canRetry && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleTaskAction(task.id, 'retry')}
                              disabled={isActioning}
                              className="text-green-600 hover:text-green-700"
                              title="Retry Task"
                            >
                              <RotateCcw className="h-3 w-3 mr-1" />
                              Retry
                            </Button>
                          )}
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => toggleTaskExpansion(task.id)}
                            title="More Actions"
                          >
                            <Edit className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>

                      {/* Expanded Actions */}
                      {isExpanded && (
                        <div className="mt-3 pt-3 border-t border-gray-200">
                          <div className="flex items-center gap-2 mb-3">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleTaskAction(task.id, 'delete')}
                              disabled={isActioning}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-3 w-3 mr-1" />
                              Delete
                            </Button>
                            {task.lastAttempt && (
                              <span className="text-xs text-gray-500">
                                Last attempt: {safeFormatRelativeTime(task.lastAttempt)}
                              </span>
                            )}
                          </div>
                        </div>
                      )}

                      {task.errorMessage && (
                        <div className="mt-3 p-3 bg-red-100 border border-red-200 rounded-md">
                          <div className="flex items-start gap-2">
                            <AlertTriangle className="h-4 w-4 text-red-600 mt-0.5 flex-shrink-0" />
                            <div>
                              <p className="text-sm font-medium text-red-800 mb-1">Error Details:</p>
                              <p className="text-sm text-red-700">{task.errorMessage}</p>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            )}
          </CollapsibleContent>
        </Collapsible>

        {/* Completed Tasks Swimlane */}
        <Collapsible defaultOpen={DEFAULT_SWIMLANE_STATES.completed}>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" className="w-full justify-between p-4 h-auto">
              <div className="flex items-center space-x-3">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span className="font-semibold">Completed Tasks</span>
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  {completedTasks?.length || 0} completed
                </Badge>
              </div>
              <ChevronRight className="h-4 w-4 transition-transform group-data-[state=open]:rotate-90" />
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-3 mt-2">
            {(completedTasks?.length || 0) === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <CheckCircle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p>No completed tasks in recent history</p>
              </div>
            ) : (
              <div className="space-y-3">
                {(completedTasks || []).slice(0, 20).map((task) => {
                  const typeColor = TaskTypeColors[task.type] || TaskTypeColors.default;

                  return (
                    <div key={task.id} className="border rounded-lg p-4 bg-green-50/30 border-green-200">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <Badge className={`${typeColor.bg} ${typeColor.text} ${typeColor.border}`}>
                            {getTaskTypeDisplayName(task.type)}
                          </Badge>
                          <div>
                            <h4 className="font-medium text-gray-900">{task.name}</h4>
                            <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                              <div className="flex items-center space-x-1">
                                <Database className="h-3 w-3" />
                                <span>{task.repository}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <User className="h-3 w-3" />
                                <span>{task.groupName}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <Clock className="h-3 w-3" />
                                <span>Completed {safeFormatRelativeTime(task.completedAt)}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <Timer className="h-3 w-3" />
                                <span>Duration: {formatDuration(task.duration)}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {task.repository && task.groupName && (
                            <Button variant="outline" size="sm" asChild>
                              <a href={`/usage/${task.repository}/${task.groupName}`} target="_blank" rel="noopener noreferrer">
                                <ExternalLink className="h-3 w-3 mr-1" />
                                View Results
                              </a>
                            </Button>
                          )}
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => toggleTaskExpansion(task.id)}
                            title="Task Details"
                          >
                            <ChevronRight className={`h-3 w-3 transition-transform ${expandedTasks.has(task.id) ? 'rotate-90' : ''}`} />
                          </Button>
                        </div>
                      </div>

                      {/* Expanded Task Details */}
                      {expandedTasks.has(task.id) && (
                        <div className="mt-3 pt-3 border-t border-gray-200">
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <span className="font-medium text-gray-700">Started:</span>
                              <span className="ml-2 text-gray-600">{formatDateTime(task.startedAt)}</span>
                            </div>
                            <div>
                              <span className="font-medium text-gray-700">Completed:</span>
                              <span className="ml-2 text-gray-600">{formatDateTime(task.completedAt)}</span>
                            </div>
                            <div>
                              <span className="font-medium text-gray-700">Duration:</span>
                              <span className="ml-2 text-gray-600">{formatDuration(task.duration)}</span>
                            </div>
                            {task.workerId && (
                              <div>
                                <span className="font-medium text-gray-700">Worker:</span>
                                <span className="ml-2 text-gray-600">{task.workerId}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Task Results Summary */}
                      {(task.resultsCount !== undefined || task.filesScanned !== undefined) && (
                        <div className="mt-3 flex items-center space-x-4 text-sm text-gray-600">
                          {task.resultsCount !== undefined && (
                            <div className="flex items-center space-x-1">
                              <CheckCircle className="h-3 w-3 text-green-600" />
                              <span className="font-medium">{task.resultsCount}</span>
                              <span>results found</span>
                            </div>
                          )}
                          {task.filesScanned !== undefined && (
                            <div className="flex items-center space-x-1">
                              <Database className="h-3 w-3 text-blue-600" />
                              <span className="font-medium">{task.filesScanned}</span>
                              <span>files scanned</span>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  );
                })}

                {(completedTasks?.length || 0) > 20 && (
                  <div className="text-center py-4">
                    <p className="text-sm text-gray-500">
                      Showing 20 most recent completed tasks out of {completedTasks?.length || 0} total
                    </p>
                  </div>
                )}
              </div>
            )}
          </CollapsibleContent>
        </Collapsible>
      </CardContent>
    </Card>
    );
  } catch (error) {
    console.error('TasksQueue render error:', error);
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            <span>Tasks Queue Error</span>
          </CardTitle>
          <CardDescription>
            An error occurred while rendering the tasks queue. Please refresh the page.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Button onClick={() => window.location.reload()} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh Page
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }
};

export default TasksQueue;
