package services

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"math/rand"
	"os"
	"path/filepath"
	"regexp"
	"sort"
	"strings"
	"sync"
	"time"

	"adgitops-ui/src/backend/models"

	"github.com/google/uuid"
)

// AutoScanService manages automated periodic scanning with vector database integration
type AutoScanService struct {
	configsDir   string
	statusDir    string
	jobsDir      string
	usageScanner UsageScanner
	repoManager  interface {
		GetRepoInstances() ([]RepoInstance, error)
		GetGroupsForRepo(repoID string) ([]models.Group, error)
		GetConfigurations() (models.RepositoryConfigList, error)
	}
	dataController interface {
		GetGroupsForRepo(repoID string) ([]models.Group, error)
	}

	configs      map[string]*models.AutoScanConfig // repoID -> config
	configsMutex sync.RWMutex

	activeJobs map[string]*models.AutoScanJob // jobID -> job
	jobsMutex  sync.RWMutex

	// Scan ID to Job ID mapping for completion tracking
	scanToJobMap map[string]string // scanID -> jobID
	scanJobMutex sync.RWMutex

	isRunning bool
	ticker    *time.Ticker
	done      chan struct{}

	// Continuous queue management
	queueTicker          *time.Ticker
	targetQueueSize      int  // Target number of pending tasks to maintain
	continuousScheduling bool // Whether continuous scheduling is enabled

	// Load balancing
	scanQueue          chan *models.AutoScanJob
	maxConcurrentScans int
	currentScans       map[string]bool // groupName:repoID -> scanning
	scansMutex         sync.RWMutex

	// Progress broadcasting
	progressBroadcaster *ProgressBroadcaster // For real-time progress updates

	// Scheduler logging
	schedulerLogs []*models.SchedulerLog
	logsMutex     sync.RWMutex
	logsDir       string

	// Vector database integration
	vectorDB *VectorDatabaseService

	// Enhanced scheduling
	schedulingConfigs map[string]*models.ScanSchedulingConfig // repoID -> scheduling config
	schedulingMutex   sync.RWMutex
}

// NewAutoScanService creates a new automated scanning service
func NewAutoScanService(dataDir string, usageScanner UsageScanner) *AutoScanService {
	configsDir := filepath.Join(dataDir, "auto_scan_configs")
	statusDir := filepath.Join(dataDir, "auto_scan_status")
	jobsDir := filepath.Join(dataDir, "auto_scan_jobs")
	logsDir := filepath.Join(dataDir, "scheduler_logs")

	// Create directories if they don't exist
	os.MkdirAll(configsDir, 0755)
	os.MkdirAll(statusDir, 0755)
	os.MkdirAll(jobsDir, 0755)
	os.MkdirAll(logsDir, 0755)

	service := &AutoScanService{
		configsDir:         configsDir,
		statusDir:          statusDir,
		jobsDir:            jobsDir,
		logsDir:            logsDir,
		usageScanner:       usageScanner,
		configs:            make(map[string]*models.AutoScanConfig),
		activeJobs:         make(map[string]*models.AutoScanJob),
		scanToJobMap:       make(map[string]string),
		done:               make(chan struct{}),
		scanQueue:          make(chan *models.AutoScanJob, 100),
		maxConcurrentScans: 5, // Default max concurrent scans
		currentScans:       make(map[string]bool),
		schedulerLogs:      make([]*models.SchedulerLog, 0),
		schedulingConfigs:  make(map[string]*models.ScanSchedulingConfig),
	}

	// Load continuous scheduling configuration from file
	continuousConfig := service.loadContinuousConfig()
	service.continuousScheduling = continuousConfig.Enabled
	service.targetQueueSize = continuousConfig.TargetQueueSize
	service.maxConcurrentScans = continuousConfig.MaxConcurrentScans

	return service
}

// SetRepoManager sets the repository manager
func (s *AutoScanService) SetRepoManager(repoManager interface {
	GetRepoInstances() ([]RepoInstance, error)
	GetGroupsForRepo(repoID string) ([]models.Group, error)
	GetConfigurations() (models.RepositoryConfigList, error)
}) {
	s.repoManager = repoManager
}

// SetDataController sets the data controller for getting real group data
func (s *AutoScanService) SetDataController(dataController interface {
	GetGroupsForRepo(repoID string) ([]models.Group, error)
}) {
	s.dataController = dataController
}

// SetVectorDatabase sets the vector database service
func (s *AutoScanService) SetVectorDatabase(vectorDB *VectorDatabaseService) {
	s.vectorDB = vectorDB
}

// Start begins the automated scanning service
func (s *AutoScanService) Start() error {
	if s.isRunning {
		log.Println("Auto scan service is already running")
		return nil
	}

	// Load existing configurations
	if err := s.loadConfigs(); err != nil {
		log.Printf("Warning: Failed to load auto scan configs: %v", err)
	}

	// Update existing configurations with corrected NextScanTime calculation
	s.updateConfigNextScanTimes()

	// Start job processor workers
	for i := 0; i < s.maxConcurrentScans; i++ {
		go s.jobProcessor(i)
	}

	// Check for scheduled scans every 5 minutes
	s.ticker = time.NewTicker(5 * time.Minute)
	s.isRunning = true

	// Start continuous queue management (checks every 2 minutes)
	s.queueTicker = time.NewTicker(2 * time.Minute)

	go s.run()
	go s.continuousQueueManager()
	log.Println("Auto scan service started with continuous queue management")
	return nil
}

// Stop stops the automated scanning service
func (s *AutoScanService) Stop() {
	if !s.isRunning {
		return
	}

	s.isRunning = false
	close(s.done)
	s.ticker.Stop()
	if s.queueTicker != nil {
		s.queueTicker.Stop()
	}
	close(s.scanQueue)
	log.Println("Auto scan service stopped")
}

// SetProgressBroadcaster sets the progress broadcaster for real-time updates
func (s *AutoScanService) SetProgressBroadcaster(broadcaster *ProgressBroadcaster) {
	s.progressBroadcaster = broadcaster
	log.Println("Updated progress broadcaster reference in auto scan service")
}

// run is the main loop for the auto scan service
func (s *AutoScanService) run() {
	// Run once immediately to catch up on any missed schedules
	s.checkSchedules()

	for {
		select {
		case <-s.done:
			return
		case <-s.ticker.C:
			if !s.isRunning {
				return
			}
			s.checkSchedules()
		}
	}
}

// checkSchedules checks for scheduled scans that need to run
func (s *AutoScanService) checkSchedules() {
	now := time.Now()

	s.configsMutex.RLock()
	configs := make([]*models.AutoScanConfig, 0, len(s.configs))
	for _, config := range s.configs {
		if config.Enabled {
			configs = append(configs, config)
		}
	}
	s.configsMutex.RUnlock()

	// Process regular scheduled scans
	for _, config := range configs {
		if s.shouldRunScan(config, now) {
			s.LogSchedulerDecision(models.ActionScheduled, config.RepoID, "",
				fmt.Sprintf("Config %s is due for scanning", config.ID),
				1, map[string]interface{}{
					"configId":   config.ID,
					"frequency":  config.Frequency,
					"timeWindow": config.TimeWindow,
				})

			if err := s.scheduleScansForConfig(config, now); err != nil {
				s.LogSchedulerAction(models.SchedulerLogLevelError, models.ActionFailed,
					config.RepoID, "",
					fmt.Sprintf("Failed to schedule scans for config %s: %v", config.ID, err),
					map[string]interface{}{"configId": config.ID, "error": err.Error()})
				log.Printf("Error scheduling scans for config %s: %v", config.ID, err)
			}
		} else {
			s.LogSchedulerDecision(models.ActionSkipped, config.RepoID, "",
				"Config not due for scanning",
				0, map[string]interface{}{
					"configId": config.ID,
					"reason":   "not in time window or frequency not met",
				})
		}
	}

	// Process intelligent scheduling for never-scanned and overdue groups
	s.schedulingMutex.RLock()
	schedulingConfigs := make([]*models.ScanSchedulingConfig, 0, len(s.schedulingConfigs))
	for _, schedulingConfig := range s.schedulingConfigs {
		if schedulingConfig.Enabled {
			schedulingConfigs = append(schedulingConfigs, schedulingConfig)
		}
	}
	s.schedulingMutex.RUnlock()

	for _, schedulingConfig := range schedulingConfigs {
		if err := s.processIntelligentScheduling(schedulingConfig, now); err != nil {
			log.Printf("Error processing intelligent scheduling for repo %s: %v", schedulingConfig.RepoID, err)
		}
	}
}

// shouldRunScan determines if a scan should run for the given config
func (s *AutoScanService) shouldRunScan(config *models.AutoScanConfig, now time.Time) bool {
	// Check if we're in the allowed time window
	if !config.IsInTimeWindow(now) {
		log.Printf("[SCHEDULER] Config %s (repo: %s) - Not in time window (current hour: %d, window: %d-%d)",
			config.ID, config.RepoID, now.Hour(), config.TimeWindow.StartHour, config.TimeWindow.EndHour)
		return false
	}

	// For daily scans, check if we're on or after the day when the scan should run
	if config.Frequency == "daily" {
		if config.LastScanTime != nil {
			// Convert times to the same timezone for comparison
			lastScan := *config.LastScanTime
			if config.TimeWindow.Timezone != "" {
				if loc, err := time.LoadLocation(config.TimeWindow.Timezone); err == nil {
					lastScan = lastScan.In(loc)
					now = now.In(loc)
				}
			}

			// Check if we've already scanned today
			lastScanDay := time.Date(lastScan.Year(), lastScan.Month(), lastScan.Day(), 0, 0, 0, 0, lastScan.Location())
			currentDay := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

			if !currentDay.After(lastScanDay) {
				// We've already scanned today, don't scan again
				log.Printf("[SCHEDULER] Config %s (repo: %s) - Already scanned today (last scan: %s, current day: %s)",
					config.ID, config.RepoID, lastScanDay.Format("2006-01-02"), currentDay.Format("2006-01-02"))
				return false
			}

			log.Printf("[SCHEDULER] Config %s (repo: %s) - Daily scan due (last scan day: %s, current day: %s)",
				config.ID, config.RepoID, lastScanDay.Format("2006-01-02"), currentDay.Format("2006-01-02"))
		} else {
			log.Printf("[SCHEDULER] Config %s (repo: %s) - First daily scan (no previous scan)",
				config.ID, config.RepoID)
		}
	} else {
		// For weekly/monthly scans, use the original NextScanTime logic
		if config.NextScanTime != nil && now.Before(*config.NextScanTime) {
			log.Printf("[SCHEDULER] Config %s (repo: %s) - Not yet time for %s scan (next: %s, now: %s)",
				config.ID, config.RepoID, config.Frequency,
				config.NextScanTime.Format(time.RFC3339), now.Format(time.RFC3339))
			return false
		}

		log.Printf("[SCHEDULER] Config %s (repo: %s) - %s scan due (next scan time: %s)",
			config.ID, config.RepoID, config.Frequency,
			func() string {
				if config.NextScanTime != nil {
					return config.NextScanTime.Format(time.RFC3339)
				}
				return "not set"
			}())
	}

	// Check if we haven't exceeded max scans per hour
	if config.LoadBalancing.MaxScansPerHour > 0 {
		recentJobs := s.getRecentJobs(config.RepoID, time.Hour)
		if len(recentJobs) >= config.LoadBalancing.MaxScansPerHour {
			log.Printf("[SCHEDULER] Config %s (repo: %s) - Max scans per hour exceeded (%d/%d)",
				config.ID, config.RepoID, len(recentJobs), config.LoadBalancing.MaxScansPerHour)
			return false
		}
	}

	log.Printf("[SCHEDULER] Config %s (repo: %s) - Scan approved for execution", config.ID, config.RepoID)
	return true
}

// updateConfigNextScanTimes updates existing configurations with corrected NextScanTime calculation
func (s *AutoScanService) updateConfigNextScanTimes() {
	s.configsMutex.Lock()
	defer s.configsMutex.Unlock()

	updated := false
	for _, config := range s.configs {
		if config.LastScanTime != nil {
			// Recalculate NextScanTime using the corrected logic
			newNextScanTime := config.CalculateNextScanTime(*config.LastScanTime)

			// Only update if the time has changed significantly (more than 1 minute difference)
			if config.NextScanTime == nil ||
				newNextScanTime.Sub(*config.NextScanTime).Abs() > time.Minute {

				config.NextScanTime = &newNextScanTime
				updated = true

				log.Printf("Updated NextScanTime for config %s (repo: %s) from %v to %v",
					config.ID, config.RepoID,
					func() string {
						if config.NextScanTime != nil {
							return config.NextScanTime.Format(time.RFC3339)
						}
						return "nil"
					}(),
					newNextScanTime.Format(time.RFC3339))
			}
		}
	}

	// Save updated configurations if any changes were made
	if updated {
		for _, config := range s.configs {
			if err := s.saveConfig(config); err != nil {
				log.Printf("Warning: Failed to save updated config %s: %v", config.ID, err)
			}
		}
		log.Println("Updated auto-scan configurations with corrected NextScanTime calculations")
	}
}

// scheduleScansForConfig schedules scans for a configuration
func (s *AutoScanService) scheduleScansForConfig(config *models.AutoScanConfig, now time.Time) error {
	var groups []models.Group
	var err error

	if config.ScanAllGroups {
		// Get all groups for the repository
		if s.dataController == nil {
			return fmt.Errorf("data controller not set")
		}
		groups, err = s.dataController.GetGroupsForRepo(config.RepoID)
		if err != nil {
			return fmt.Errorf("failed to get groups for repo %s: %v", config.RepoID, err)
		}
	} else {
		// Use specified target groups
		for _, groupName := range config.TargetGroups {
			groups = append(groups, models.Group{Groupname: groupName})
		}
	}

	// Apply priority-based sorting if enabled
	if config.PriorityConfig.Enabled {
		groups = s.sortGroupsByPriority(groups, config.PriorityConfig)
	}

	// Schedule jobs with load balancing
	scheduledTime := now
	for i, group := range groups {
		// Spread scans across time if load balancing is enabled
		if config.LoadBalancing.SpreadAcrossDay && len(groups) > 1 {
			// Add random offset to spread scans
			maxOffset := time.Duration(24) * time.Hour / time.Duration(len(groups))
			offset := time.Duration(rand.Int63n(int64(maxOffset)))
			scheduledTime = now.Add(offset)
		} else if config.LoadBalancing.MinIntervalBetween != "" {
			// Add minimum interval between scans
			if interval, err := time.ParseDuration(config.LoadBalancing.MinIntervalBetween); err == nil {
				scheduledTime = now.Add(time.Duration(i) * interval)
			}
		}

		job := &models.AutoScanJob{
			ID:           uuid.New().String(),
			ConfigID:     config.ID,
			RepoID:       config.RepoID,
			GroupName:    group.Groupname,
			ScheduledFor: scheduledTime,
			Status:       "pending",
			CreatedAt:    now,
		}

		// Log job creation
		s.LogJobLifecycle(models.ActionQueued, job,
			fmt.Sprintf("Created auto-scan job for group %s", group.Groupname), nil)

		// Add job to queue
		select {
		case s.scanQueue <- job:
			s.jobsMutex.Lock()
			s.activeJobs[job.ID] = job
			s.jobsMutex.Unlock()

			s.LogSchedulerAction(models.SchedulerLogLevelInfo, models.ActionQueued,
				config.RepoID, group.Groupname,
				fmt.Sprintf("Queued scan for group %s at %v", group.Groupname, scheduledTime),
				map[string]interface{}{
					"jobId":        job.ID,
					"scheduledFor": scheduledTime,
				})
			log.Printf("Scheduled auto scan job %s for group %s in repo %s at %s",
				job.ID, job.GroupName, job.RepoID, job.ScheduledFor.Format(time.RFC3339))
		default:
			s.LogSchedulerAction(models.SchedulerLogLevelWarning, models.ActionFailed,
				config.RepoID, group.Groupname,
				"Scan queue is full, could not schedule scan",
				map[string]interface{}{
					"jobId":         job.ID,
					"queueCapacity": cap(s.scanQueue),
				})
			log.Printf("Warning: Scan queue is full, skipping job for group %s", group.Groupname)
		}
	}

	// Update next scan time
	nextScan := config.CalculateNextScanTime(now)
	config.NextScanTime = &nextScan
	config.LastScanTime = &now

	// Save updated config
	if err := s.saveConfig(config); err != nil {
		log.Printf("Warning: Failed to save updated config %s: %v", config.ID, err)
	}

	return nil
}

// processIntelligentScheduling processes intelligent scheduling for never-scanned and overdue groups
func (s *AutoScanService) processIntelligentScheduling(config *models.ScanSchedulingConfig, now time.Time) error {
	if s.vectorDB == nil {
		log.Printf("Vector database not available for intelligent scheduling")
		return nil
	}

	if s.dataController == nil {
		return fmt.Errorf("data controller not set")
	}

	// Check if we're in the allowed time window
	if !s.isInTimeWindow(config.DailyScheduleWindow, now) {
		return nil
	}

	// Get all groups for the repository
	allGroups, err := s.dataController.GetGroupsForRepo(config.RepoID)
	if err != nil {
		return fmt.Errorf("failed to get groups for repo %s: %w", config.RepoID, err)
	}

	ctx := context.Background()

	// Find never-scanned groups
	neverScannedGroups, err := s.vectorDB.GetNeverScannedGroups(ctx, allGroups, config.RepoID)
	if err != nil {
		log.Printf("Warning: Failed to get never-scanned groups: %v", err)
		neverScannedGroups = []models.Group{} // Continue with empty list
	} else {
		s.LogSchedulerDecision(models.ActionScheduled, config.RepoID, "",
			fmt.Sprintf("Found %d never-scanned groups for intelligent scheduling", len(neverScannedGroups)),
			1, map[string]interface{}{
				"neverScannedCount": len(neverScannedGroups),
				"totalGroups":       len(allGroups),
			})
	}

	// Find groups not scanned within threshold
	overdueGroups, err := s.vectorDB.GetGroupsNotScannedSince(ctx, allGroups, config.RepoID, config.ScanFrequencyThreshold)
	if err != nil {
		log.Printf("Warning: Failed to get overdue groups: %v", err)
		overdueGroups = []models.Group{} // Continue with empty list
	} else {
		s.LogSchedulerDecision(models.ActionScheduled, config.RepoID, "",
			fmt.Sprintf("Found %d overdue groups (threshold: %v) for intelligent scheduling", len(overdueGroups), config.ScanFrequencyThreshold),
			2, map[string]interface{}{
				"overdueCount": len(overdueGroups),
				"threshold":    config.ScanFrequencyThreshold.String(),
				"totalGroups":  len(allGroups),
			})
	}

	// Prioritize and schedule groups
	prioritizedGroups := s.prioritizeGroups(neverScannedGroups, overdueGroups, config)

	// Schedule scans with load balancing
	return s.scheduleIntelligentScans(prioritizedGroups, config, now)
}

// prioritizeGroups prioritizes groups based on rules and never-scanned status
func (s *AutoScanService) prioritizeGroups(neverScanned, overdue []models.Group, config *models.ScanSchedulingConfig) []models.Group {
	type prioritizedGroup struct {
		group    models.Group
		priority int // 1=high, 2=medium, 3=low
		reason   string
	}

	var prioritized []prioritizedGroup

	// Add never-scanned groups with high priority
	neverScannedPriority := s.getPriorityValue(config.NeverScannedPriority)
	for _, group := range neverScanned {
		prioritized = append(prioritized, prioritizedGroup{
			group:    group,
			priority: neverScannedPriority,
			reason:   "never_scanned",
		})
	}

	// Add overdue groups with priority based on rules
	for _, group := range overdue {
		// Skip if already in never-scanned list
		isNeverScanned := false
		for _, neverGroup := range neverScanned {
			if neverGroup.Groupname == group.Groupname {
				isNeverScanned = true
				break
			}
		}
		if isNeverScanned {
			continue
		}

		priority := s.getGroupPriority(group.Groupname, config.GroupPriorityRules)
		prioritized = append(prioritized, prioritizedGroup{
			group:    group,
			priority: priority,
			reason:   "overdue",
		})
	}

	// Sort by priority (lower number = higher priority)
	sort.Slice(prioritized, func(i, j int) bool {
		return prioritized[i].priority < prioritized[j].priority
	})

	// Extract groups in priority order
	result := make([]models.Group, len(prioritized))
	for i, pg := range prioritized {
		result[i] = pg.group
		s.LogSchedulerDecision(models.ActionScheduled, config.RepoID, pg.group.Groupname,
			fmt.Sprintf("Intelligent scheduling: prioritized group (priority: %d, reason: %s)", pg.priority, pg.reason),
			pg.priority, map[string]interface{}{
				"priority": pg.priority,
				"reason":   pg.reason,
				"position": i + 1,
			})
		log.Printf("Prioritized group %s (priority: %d, reason: %s)", pg.group.Groupname, pg.priority, pg.reason)
	}

	return result
}

// scheduleIntelligentScans schedules scans for prioritized groups with load balancing
func (s *AutoScanService) scheduleIntelligentScans(groups []models.Group, config *models.ScanSchedulingConfig, now time.Time) error {
	if len(groups) == 0 {
		return nil
	}

	// Check load balancing constraints
	if config.LoadBalancing.MaxScansPerHour > 0 {
		recentJobs := s.getRecentJobs(config.RepoID, time.Hour)
		if len(recentJobs) >= config.LoadBalancing.MaxScansPerHour {
			log.Printf("Skipping intelligent scheduling for repo %s - max scans per hour reached", config.RepoID)
			return nil
		}
	}

	// Calculate scheduling intervals
	scheduledTime := now
	maxGroupsToSchedule := config.LoadBalancing.MaxScansPerHour
	if maxGroupsToSchedule <= 0 {
		maxGroupsToSchedule = 10 // Default limit
	}

	scheduledCount := 0
	for i, group := range groups {
		if scheduledCount >= maxGroupsToSchedule {
			break
		}

		// Add interval between scans
		if config.LoadBalancing.MinIntervalBetween != "" && i > 0 {
			if interval, err := time.ParseDuration(config.LoadBalancing.MinIntervalBetween); err == nil {
				scheduledTime = scheduledTime.Add(interval)
			}
		}

		// Create intelligent scan job
		job := &models.AutoScanJob{
			ID:           uuid.New().String(),
			ConfigID:     config.ID,
			RepoID:       config.RepoID,
			GroupName:    group.Groupname,
			ScheduledFor: scheduledTime,
			Status:       "pending",
			CreatedAt:    now,
		}

		// Add job to queue
		select {
		case s.scanQueue <- job:
			s.jobsMutex.Lock()
			s.activeJobs[job.ID] = job
			s.jobsMutex.Unlock()
			log.Printf("Scheduled intelligent scan job %s for group %s in repo %s at %s",
				job.ID, job.GroupName, job.RepoID, job.ScheduledFor.Format(time.RFC3339))
			scheduledCount++
		default:
			log.Printf("Warning: Scan queue is full, stopping intelligent scheduling")
			break
		}
	}

	log.Printf("Scheduled %d intelligent scans for repo %s", scheduledCount, config.RepoID)
	return nil
}

// jobProcessor processes scan jobs from the queue
func (s *AutoScanService) jobProcessor(workerID int) {
	log.Printf("Auto scan job processor %d started", workerID)

	for job := range s.scanQueue {
		if !s.isRunning {
			break
		}

		// Wait until scheduled time
		if time.Now().Before(job.ScheduledFor) {
			time.Sleep(time.Until(job.ScheduledFor))
		}

		// Check if we can run this scan (not already scanning this group)
		scanKey := fmt.Sprintf("%s:%s", job.GroupName, job.RepoID)
		s.scansMutex.Lock()
		if s.currentScans[scanKey] {
			s.scansMutex.Unlock()
			log.Printf("Skipping auto scan for %s - already in progress", scanKey)
			job.Status = "skipped"

			// Remove from active jobs since we're skipping it
			s.jobsMutex.Lock()
			delete(s.activeJobs, job.ID)
			s.jobsMutex.Unlock()
			continue
		}
		s.currentScans[scanKey] = true
		s.scansMutex.Unlock()

		log.Printf("Starting auto scan job %s for group %s in repo %s", job.ID, job.GroupName, job.RepoID)

		// Process the job
		s.processJob(job, workerID)

		// Mark scan as complete and clean up
		s.scansMutex.Lock()
		delete(s.currentScans, scanKey)
		s.scansMutex.Unlock()

		// Remove from active jobs
		s.jobsMutex.Lock()
		delete(s.activeJobs, job.ID)
		s.jobsMutex.Unlock()

		log.Printf("Completed auto scan job %s for group %s in repo %s", job.ID, job.GroupName, job.RepoID)
	}

	log.Printf("Auto scan job processor %d stopped", workerID)
}

// processJob processes a single scan job
func (s *AutoScanService) processJob(job *models.AutoScanJob, workerID int) {
	startTime := time.Now()
	job.Status = "running"
	job.StartedAt = &startTime

	log.Printf("Worker %d: Starting auto scan for group %s in repo %s",
		workerID, job.GroupName, job.RepoID)

	// Create scan request
	request := models.UsageScanRequest{
		GroupName: job.GroupName,
		RepoID:    job.RepoID,
		Force:     false, // Don't force if already scanning
	}

	// Execute the scan with a more reasonable timeout (10 minutes)
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
	defer cancel()

	log.Printf("Worker %d: Executing scan request for group %s in repo %s", workerID, job.GroupName, job.RepoID)
	actualScanID, err := s.usageScanner.ScanGroupUsage(ctx, request)
	if err == nil {
		log.Printf("Worker %d: Auto scan started successfully with scan ID: %s", workerID, actualScanID)
	} else {
		log.Printf("Worker %d: Failed to start auto scan for group %s in repo %s: %v", workerID, job.GroupName, job.RepoID, err)
	}

	completedTime := time.Now()
	job.CompletedAt = &completedTime

	if err != nil {
		job.Status = "failed"
		job.Error = err.Error()
		log.Printf("Worker %d: Auto scan failed for group %s in repo %s: %v",
			workerID, job.GroupName, job.RepoID, err)
	} else {
		job.Status = "completed"

		// Get results count
		if results, err := s.usageScanner.GetUsageResults(job.GroupName, job.RepoID, 1, 1); err == nil {
			job.ResultsCount = results.Total
		}

		log.Printf("Worker %d: Auto scan completed for group %s in repo %s in %v",
			workerID, job.GroupName, job.RepoID, completedTime.Sub(startTime))
	}

	// Save job status
	if err := s.saveJob(job); err != nil {
		log.Printf("Warning: Failed to save job %s: %v", job.ID, err)
	}
}

// Configuration management methods
func (s *AutoScanService) CreateConfig(config *models.AutoScanConfig) error {
	if err := config.Validate(); err != nil {
		return err
	}

	config.ID = uuid.New().String()
	config.CreatedAt = time.Now()
	config.UpdatedAt = time.Now()

	// Calculate initial next scan time
	nextScan := config.CalculateNextScanTime(time.Now())
	config.NextScanTime = &nextScan

	s.configsMutex.Lock()
	s.configs[config.RepoID] = config
	s.configsMutex.Unlock()

	return s.saveConfig(config)
}

// GetConfig returns the configuration for a specific repository
func (s *AutoScanService) GetConfig(repoID string) (*models.AutoScanConfig, error) {
	s.configsMutex.RLock()
	defer s.configsMutex.RUnlock()

	config, exists := s.configs[repoID]
	if !exists {
		return nil, fmt.Errorf("configuration not found for repository %s", repoID)
	}

	// Return a copy to prevent external modifications
	configCopy := *config
	return &configCopy, nil
}

// GetAllConfigs returns all auto-scan configurations
func (s *AutoScanService) GetAllConfigs() map[string]*models.AutoScanConfig {
	s.configsMutex.RLock()
	defer s.configsMutex.RUnlock()

	// Return a copy to prevent external modifications
	configs := make(map[string]*models.AutoScanConfig)
	for repoID, config := range s.configs {
		configCopy := *config
		configs[repoID] = &configCopy
	}

	return configs
}

// UpdateConfig updates an existing configuration
func (s *AutoScanService) UpdateConfig(config *models.AutoScanConfig) error {
	if err := config.Validate(); err != nil {
		return err
	}

	s.configsMutex.Lock()
	defer s.configsMutex.Unlock()

	existingConfig, exists := s.configs[config.RepoID]
	if !exists {
		return fmt.Errorf("configuration not found for repository %s", config.RepoID)
	}

	// Preserve creation time and ID
	config.ID = existingConfig.ID
	config.CreatedAt = existingConfig.CreatedAt
	config.UpdatedAt = time.Now()

	// Recalculate next scan time if frequency or time window changed
	if config.Frequency != existingConfig.Frequency ||
		config.TimeWindow != existingConfig.TimeWindow {
		nextScan := config.CalculateNextScanTime(time.Now())
		config.NextScanTime = &nextScan
	}

	s.configs[config.RepoID] = config
	return s.saveConfig(config)
}

// DeleteConfig deletes a configuration for a repository
func (s *AutoScanService) DeleteConfig(repoID string) error {
	s.configsMutex.Lock()
	defer s.configsMutex.Unlock()

	_, exists := s.configs[repoID]
	if !exists {
		return fmt.Errorf("configuration not found for repository %s", repoID)
	}

	delete(s.configs, repoID)

	// Remove the configuration file
	configPath := filepath.Join(s.configsDir, fmt.Sprintf("%s.json", repoID))
	if err := os.Remove(configPath); err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("failed to remove config file: %v", err)
	}

	return nil
}

// EnableAutoScan enables auto-scanning for a repository with default settings
func (s *AutoScanService) EnableAutoScan(repoID string) error {
	// Check if config already exists
	s.configsMutex.RLock()
	_, exists := s.configs[repoID]
	s.configsMutex.RUnlock()

	if exists {
		return fmt.Errorf("auto-scan configuration already exists for repository %s", repoID)
	}

	// Create default configuration
	config := &models.AutoScanConfig{
		RepoID:    repoID,
		Enabled:   true,
		Frequency: "daily",
		TimeWindow: models.TimeWindow{
			StartHour: 2, // 2 AM
			EndHour:   6, // 6 AM
			Timezone:  "UTC",
		},
		MaxConcurrentScans: 3,
		ScanAllGroups:      true,
		TargetGroups:       []string{},
		LoadBalancing: models.LoadBalancingConfig{
			SpreadAcrossDay:    true,
			MinIntervalBetween: "30m",
			MaxScansPerHour:    10,
		},
		NotificationConfig: models.NotificationConfig{
			OnCompletion: false,
			OnFailure:    true,
			OnSummary:    false,
		},
		PriorityConfig: models.PriorityConfig{
			Enabled:          false,
			DefaultWeight:    50,
			ScheduleByWeight: true,
			Rules: []models.PriorityRule{
				{
					ID:          "high-priority-admin",
					Name:        "High Priority Admin Groups",
					Pattern:     "*Admin*",
					Weight:      100,
					Description: "Administrative groups get highest priority",
					Enabled:     true,
					Operator:    models.PriorityRuleInclude,
				},
				{
					ID:          "medium-priority-super",
					Name:        "Medium Priority Super User Groups",
					Pattern:     "*Super*",
					Weight:      75,
					Description: "Super user groups get medium-high priority",
					Enabled:     true,
					Operator:    models.PriorityRuleInclude,
				},
				{
					ID:          "low-priority-standard",
					Name:        "Low Priority Standard Groups",
					Pattern:     "*Standard*",
					Weight:      25,
					Description: "Standard user groups get lower priority",
					Enabled:     true,
					Operator:    models.PriorityRuleInclude,
				},
			},
		},
	}

	return s.CreateConfig(config)
}

// DisableAutoScan disables auto-scanning for a repository
func (s *AutoScanService) DisableAutoScan(repoID string) error {
	s.configsMutex.Lock()
	defer s.configsMutex.Unlock()

	config, exists := s.configs[repoID]
	if !exists {
		return fmt.Errorf("configuration not found for repository %s", repoID)
	}

	config.Enabled = false
	config.UpdatedAt = time.Now()

	return s.saveConfig(config)
}

// Scheduler logging methods

// LogSchedulerAction logs a scheduler action with the specified details
func (s *AutoScanService) LogSchedulerAction(level models.SchedulerLogLevel, action models.SchedulerLogAction, repoID, groupName, message string, metadata map[string]interface{}) {
	logEntry := &models.SchedulerLog{
		ID:        uuid.New().String(),
		Timestamp: time.Now(),
		Level:     level,
		Action:    action,
		RepoID:    repoID,
		GroupName: groupName,
		Message:   message,
		Metadata:  metadata,
	}

	s.logsMutex.Lock()
	s.schedulerLogs = append(s.schedulerLogs, logEntry)

	// Keep only the last 1000 logs in memory to prevent memory leaks
	if len(s.schedulerLogs) > 1000 {
		s.schedulerLogs = s.schedulerLogs[len(s.schedulerLogs)-1000:]
	}
	s.logsMutex.Unlock()

	// Save to disk asynchronously
	go s.saveLogEntry(logEntry)

	// Log to console for debugging
	log.Printf("[SCHEDULER] %s - %s: %s (repo: %s, group: %s)",
		strings.ToUpper(string(level)),
		strings.ToUpper(string(action)),
		message,
		repoID,
		groupName)
}

// LogSchedulerDecision logs a scheduling decision with reason and priority
func (s *AutoScanService) LogSchedulerDecision(action models.SchedulerLogAction, repoID, groupName, reason string, priority int, metadata map[string]interface{}) {
	if metadata == nil {
		metadata = make(map[string]interface{})
	}
	metadata["priority"] = priority

	logEntry := &models.SchedulerLog{
		ID:        uuid.New().String(),
		Timestamp: time.Now(),
		Level:     models.SchedulerLogLevelInfo,
		Action:    action,
		RepoID:    repoID,
		GroupName: groupName,
		Message:   fmt.Sprintf("Scheduler decision: %s", action),
		Reason:    reason,
		Priority:  priority,
		Metadata:  metadata,
	}

	s.logsMutex.Lock()
	s.schedulerLogs = append(s.schedulerLogs, logEntry)

	// Keep only the last 1000 logs in memory
	if len(s.schedulerLogs) > 1000 {
		s.schedulerLogs = s.schedulerLogs[len(s.schedulerLogs)-1000:]
	}
	s.logsMutex.Unlock()

	// Save to disk asynchronously
	go s.saveLogEntry(logEntry)

	log.Printf("[SCHEDULER] Decision: %s for %s/%s - %s (priority: %d)",
		action, repoID, groupName, reason, priority)
}

// LogJobLifecycle logs job lifecycle events (created, started, completed, failed)
func (s *AutoScanService) LogJobLifecycle(action models.SchedulerLogAction, job *models.AutoScanJob, message string, err error) {
	level := models.SchedulerLogLevelInfo
	if err != nil {
		level = models.SchedulerLogLevelError
	}

	metadata := map[string]interface{}{
		"jobId":        job.ID,
		"configId":     job.ConfigID,
		"scheduledFor": job.ScheduledFor,
		"status":       job.Status,
	}

	if job.StartedAt != nil {
		metadata["startedAt"] = *job.StartedAt
	}
	if job.CompletedAt != nil {
		metadata["completedAt"] = *job.CompletedAt
		if job.StartedAt != nil {
			duration := job.CompletedAt.Sub(*job.StartedAt)
			metadata["duration"] = duration.String()
		}
	}

	logEntry := &models.SchedulerLog{
		ID:        uuid.New().String(),
		Timestamp: time.Now(),
		Level:     level,
		Action:    action,
		RepoID:    job.RepoID,
		GroupName: job.GroupName,
		JobID:     job.ID,
		ConfigID:  job.ConfigID,
		Message:   message,
		Metadata:  metadata,
	}

	if err != nil {
		logEntry.Error = err.Error()
	}

	s.logsMutex.Lock()
	s.schedulerLogs = append(s.schedulerLogs, logEntry)

	// Keep only the last 1000 logs in memory
	if len(s.schedulerLogs) > 1000 {
		s.schedulerLogs = s.schedulerLogs[len(s.schedulerLogs)-1000:]
	}
	s.logsMutex.Unlock()

	// Save to disk asynchronously
	go s.saveLogEntry(logEntry)
}

// GetSchedulerLogs returns scheduler logs with optional filtering
func (s *AutoScanService) GetSchedulerLogs(filter *models.SchedulerLogFilter) ([]*models.SchedulerLog, error) {
	s.logsMutex.RLock()
	defer s.logsMutex.RUnlock()

	var filteredLogs []*models.SchedulerLog

	for _, logEntry := range s.schedulerLogs {
		if logEntry.Matches(filter) {
			// Create a copy to prevent external modifications
			logCopy := *logEntry
			filteredLogs = append(filteredLogs, &logCopy)
		}
	}

	// Apply limit and offset
	if filter != nil {
		if filter.Offset > 0 && filter.Offset < len(filteredLogs) {
			filteredLogs = filteredLogs[filter.Offset:]
		}
		if filter.Limit > 0 && filter.Limit < len(filteredLogs) {
			filteredLogs = filteredLogs[:filter.Limit]
		}
	}

	return filteredLogs, nil
}

// ClearSchedulerLogs clears all scheduler logs
func (s *AutoScanService) ClearSchedulerLogs() {
	s.logsMutex.Lock()
	defer s.logsMutex.Unlock()

	s.schedulerLogs = make([]*models.SchedulerLog, 0)
}

// saveLogEntry saves a log entry to disk
func (s *AutoScanService) saveLogEntry(logEntry *models.SchedulerLog) error {
	data, err := json.MarshalIndent(logEntry, "", "  ")
	if err != nil {
		log.Printf("Warning: Failed to marshal log entry %s: %v", logEntry.ID, err)
		return err
	}

	// Create filename with timestamp and ID for uniqueness
	filename := fmt.Sprintf("%s_%s.json",
		logEntry.Timestamp.Format("2006-01-02_15-04-05"),
		logEntry.ID[:8])
	logPath := filepath.Join(s.logsDir, filename)

	if err := os.WriteFile(logPath, data, 0644); err != nil {
		log.Printf("Warning: Failed to save log entry %s: %v", logEntry.ID, err)
		return err
	}

	return nil
}

// Helper methods for persistence
func (s *AutoScanService) loadConfigs() error {
	files, err := os.ReadDir(s.configsDir)
	if err != nil {
		return err
	}

	for _, file := range files {
		if filepath.Ext(file.Name()) == ".json" {
			configPath := filepath.Join(s.configsDir, file.Name())
			data, err := os.ReadFile(configPath)
			if err != nil {
				log.Printf("Warning: Failed to read config file %s: %v", configPath, err)
				continue
			}

			var config models.AutoScanConfig
			if err := json.Unmarshal(data, &config); err != nil {
				log.Printf("Warning: Failed to parse config file %s: %v", configPath, err)
				continue
			}

			s.configsMutex.Lock()
			s.configs[config.RepoID] = &config
			s.configsMutex.Unlock()
		}
	}

	return nil
}

func (s *AutoScanService) saveConfig(config *models.AutoScanConfig) error {
	config.UpdatedAt = time.Now()

	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return err
	}

	configPath := filepath.Join(s.configsDir, fmt.Sprintf("%s.json", config.RepoID))
	return os.WriteFile(configPath, data, 0644)
}

func (s *AutoScanService) saveJob(job *models.AutoScanJob) error {
	data, err := json.MarshalIndent(job, "", "  ")
	if err != nil {
		return err
	}

	jobPath := filepath.Join(s.jobsDir, fmt.Sprintf("%s.json", job.ID))
	return os.WriteFile(jobPath, data, 0644)
}

func (s *AutoScanService) getRecentJobs(repoID string, duration time.Duration) []*models.AutoScanJob {
	// Implementation would scan job files and return recent jobs
	// For brevity, returning empty slice
	return []*models.AutoScanJob{}
}

// isInTimeWindow checks if the current time is within the allowed time window
func (s *AutoScanService) isInTimeWindow(window models.TimeWindow, now time.Time) bool {
	// For simplicity, assume UTC timezone for now
	// In production, this should handle timezone conversion properly
	hour := now.Hour()

	if window.StartHour <= window.EndHour {
		// Same day window (e.g., 9 AM to 5 PM)
		return hour >= window.StartHour && hour < window.EndHour
	} else {
		// Overnight window (e.g., 10 PM to 6 AM)
		return hour >= window.StartHour || hour < window.EndHour
	}
}

// getPriorityValue converts priority string to numeric value
func (s *AutoScanService) getPriorityValue(priority string) int {
	switch priority {
	case "high":
		return 1
	case "medium":
		return 2
	case "low":
		return 3
	default:
		return 2 // Default to medium
	}
}

// getGroupPriority determines priority for a group based on naming rules
func (s *AutoScanService) getGroupPriority(groupName string, rules []models.GroupPriorityRule) int {
	for _, rule := range rules {
		// Use the same pattern matching logic as the regular priority system
		if s.matchesPattern(groupName, rule.Pattern) {
			return s.getPriorityValue(rule.Priority)
		}
	}
	return 2 // Default to medium priority
}

// Scheduling configuration management methods

// CreateSchedulingConfig creates a new scheduling configuration
func (s *AutoScanService) CreateSchedulingConfig(config *models.ScanSchedulingConfig) error {
	config.ID = uuid.New().String()
	config.CreatedAt = time.Now()
	config.UpdatedAt = time.Now()

	s.schedulingMutex.Lock()
	s.schedulingConfigs[config.RepoID] = config
	s.schedulingMutex.Unlock()

	return s.saveSchedulingConfig(config)
}

// UpdateSchedulingConfig updates an existing scheduling configuration
func (s *AutoScanService) UpdateSchedulingConfig(config *models.ScanSchedulingConfig) error {
	config.UpdatedAt = time.Now()

	s.schedulingMutex.Lock()
	s.schedulingConfigs[config.RepoID] = config
	s.schedulingMutex.Unlock()

	return s.saveSchedulingConfig(config)
}

// GetSchedulingConfig gets a scheduling configuration by repo ID
func (s *AutoScanService) GetSchedulingConfig(repoID string) (*models.ScanSchedulingConfig, error) {
	s.schedulingMutex.RLock()
	config, exists := s.schedulingConfigs[repoID]
	s.schedulingMutex.RUnlock()

	if !exists {
		return nil, fmt.Errorf("scheduling config not found for repo %s", repoID)
	}

	return config, nil
}

// DeleteSchedulingConfig deletes a scheduling configuration
func (s *AutoScanService) DeleteSchedulingConfig(repoID string) error {
	s.schedulingMutex.Lock()
	delete(s.schedulingConfigs, repoID)
	s.schedulingMutex.Unlock()

	// Remove config file
	configPath := filepath.Join(s.configsDir, fmt.Sprintf("scheduling_%s.json", repoID))
	return os.Remove(configPath)
}

// saveSchedulingConfig saves a scheduling configuration to disk
func (s *AutoScanService) saveSchedulingConfig(config *models.ScanSchedulingConfig) error {
	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return err
	}

	configPath := filepath.Join(s.configsDir, fmt.Sprintf("scheduling_%s.json", config.RepoID))
	return os.WriteFile(configPath, data, 0644)
}

// GetActiveJobs returns all currently active jobs
func (s *AutoScanService) GetActiveJobs() []*models.AutoScanJob {
	s.jobsMutex.RLock()
	jobs := make([]*models.AutoScanJob, 0, len(s.activeJobs))
	for _, job := range s.activeJobs {
		jobs = append(jobs, job)
	}
	s.jobsMutex.RUnlock()

	// Return only real jobs - no sample jobs for production
	// Sample jobs were causing issues because they weren't properly integrated
	// with the scheduler's job processing system
	log.Printf("GetActiveJobs: Returning %d real active jobs", len(jobs))
	return jobs
}

// sortGroupsByPriority sorts groups based on priority configuration with enhanced operator support and age-based secondary sorting
func (s *AutoScanService) sortGroupsByPriority(groups []models.Group, priorityConfig models.PriorityConfig) []models.Group {
	// First apply priority rules with operators to filter groups
	filteredGroups := s.applyPriorityRules(groups, priorityConfig)

	// Then sort the filtered groups by priority and age
	type groupWithPriorityAndAge struct {
		group        models.Group
		priority     int
		lastScanTime *time.Time
	}

	groupsWithPriorityAndAge := make([]groupWithPriorityAndAge, len(filteredGroups))
	for i, group := range filteredGroups {
		priority := s.calculateGroupPriority(group.Groupname, priorityConfig)

		// Get last scan time for age-based sorting
		var lastScanTime *time.Time
		if s.vectorDB != nil {
			// Get the last scan time for this group from the vector database
			ctx := context.Background()
			if scanTime, err := s.vectorDB.GetLastScanTime(ctx, group.Groupname, group.RepoID); err == nil && !scanTime.IsZero() {
				lastScanTime = &scanTime
			}
		}

		groupsWithPriorityAndAge[i] = groupWithPriorityAndAge{
			group:        group,
			priority:     priority,
			lastScanTime: lastScanTime,
		}
	}

	// Sort by priority first, then by age (oldest first) as secondary criteria
	sort.Slice(groupsWithPriorityAndAge, func(i, j int) bool {
		groupI := groupsWithPriorityAndAge[i]
		groupJ := groupsWithPriorityAndAge[j]

		// Primary sort: by priority
		if groupI.priority != groupJ.priority {
			if priorityConfig.ScheduleByWeight {
				return groupI.priority > groupJ.priority // Higher priority first
			}
			return groupI.priority < groupJ.priority // Lower priority number = higher priority
		}

		// Secondary sort: by age (oldest first when priorities are equal)
		// Groups that have never been scanned get highest priority
		if groupI.lastScanTime == nil && groupJ.lastScanTime == nil {
			// Both never scanned, maintain original order
			return false
		}
		if groupI.lastScanTime == nil {
			// Group i never scanned, should come first
			return true
		}
		if groupJ.lastScanTime == nil {
			// Group j never scanned, should come first
			return false
		}

		// Both have been scanned, oldest scan time comes first
		return groupI.lastScanTime.Before(*groupJ.lastScanTime)
	})

	// Extract the sorted groups
	sortedGroups := make([]models.Group, len(groupsWithPriorityAndAge))
	for i, gwpa := range groupsWithPriorityAndAge {
		sortedGroups[i] = gwpa.group
	}

	// Log the priority-based sorting with age consideration
	log.Printf("Priority-based sorting with age consideration applied: %d groups sorted", len(sortedGroups))
	logLimit := 5
	if len(sortedGroups) < logLimit {
		logLimit = len(sortedGroups)
	}
	for i, group := range sortedGroups[:logLimit] { // Log first 5 groups
		gwpa := groupsWithPriorityAndAge[i]
		ageStr := "never scanned"
		if gwpa.lastScanTime != nil {
			ageStr = fmt.Sprintf("last scanned %v ago", time.Since(*gwpa.lastScanTime).Truncate(time.Minute))
		}
		log.Printf("  %d. %s (priority: %d, %s)", i+1, group.Groupname, gwpa.priority, ageStr)
	}

	return sortedGroups
}

// calculateGroupPriority calculates the priority weight for a group based on priority rules
func (s *AutoScanService) calculateGroupPriority(groupName string, priorityConfig models.PriorityConfig) int {
	// Start with default weight
	priority := priorityConfig.DefaultWeight

	// Ensure backward compatibility for rules without operators
	rules := s.ensureRuleDefaults(priorityConfig.Rules)

	// Check each rule to find the highest matching priority
	for _, rule := range rules {
		if !rule.Enabled {
			continue
		}

		if s.matchesPattern(groupName, rule.Pattern) {
			// Use the highest matching priority
			if rule.Weight > priority {
				priority = rule.Weight
			}
		}
	}

	return priority
}

// ensureRuleDefaults ensures all rules have default operators for backward compatibility
func (s *AutoScanService) ensureRuleDefaults(rules []models.PriorityRule) []models.PriorityRule {
	result := make([]models.PriorityRule, len(rules))
	for i, rule := range rules {
		result[i] = rule
		// Set default operator if not specified (backward compatibility)
		if result[i].Operator == "" {
			result[i].Operator = models.PriorityRuleInclude
		}
	}
	return result
}

// applyPriorityRules applies enhanced priority rules with operators to filter and prioritize groups
func (s *AutoScanService) applyPriorityRules(groups []models.Group, priorityConfig models.PriorityConfig) []models.Group {
	if !priorityConfig.Enabled || len(priorityConfig.Rules) == 0 {
		return groups
	}

	// Ensure all rules have default operators for backward compatibility
	rules := s.ensureRuleDefaults(priorityConfig.Rules)

	// Sort rules by weight (highest first) to process in priority order
	sortedRules := make([]models.PriorityRule, len(rules))
	copy(sortedRules, rules)
	sort.Slice(sortedRules, func(i, j int) bool {
		return sortedRules[i].Weight > sortedRules[j].Weight
	})

	var result []models.Group
	excludedGroups := make(map[string]bool)
	requiredGroups := make(map[string]bool)
	hasExclusiveRules := false

	// First pass: identify excluded and required groups, check for exclusive rules
	for _, rule := range sortedRules {
		if !rule.Enabled {
			continue
		}

		if rule.Operator == models.PriorityRuleExclusive {
			hasExclusiveRules = true
		}

		for _, group := range groups {
			if s.matchesPattern(group.Groupname, rule.Pattern) {
				switch rule.Operator {
				case models.PriorityRuleExclude:
					excludedGroups[group.Groupname] = true
					s.LogSchedulerDecision(models.ActionSkipped, "", group.Groupname,
						fmt.Sprintf("Group excluded by rule '%s' (pattern: %s)", rule.Name, rule.Pattern),
						0, map[string]interface{}{
							"ruleId":   rule.ID,
							"pattern":  rule.Pattern,
							"operator": rule.Operator,
						})
				case models.PriorityRuleRequired:
					requiredGroups[group.Groupname] = true
					s.LogSchedulerDecision(models.ActionScheduled, "", group.Groupname,
						fmt.Sprintf("Group required by rule '%s' (pattern: %s)", rule.Name, rule.Pattern),
						rule.Weight, map[string]interface{}{
							"ruleId":   rule.ID,
							"pattern":  rule.Pattern,
							"operator": rule.Operator,
							"weight":   rule.Weight,
						})
				}
			}
		}
	}

	// Second pass: build the result list based on rules
	for _, group := range groups {
		groupName := group.Groupname

		// Skip excluded groups (unless they're required)
		if excludedGroups[groupName] && !requiredGroups[groupName] {
			continue
		}

		// If there are exclusive rules, only include groups that match at least one exclusive rule or are required
		if hasExclusiveRules {
			matchesExclusive := false
			for _, rule := range sortedRules {
				if !rule.Enabled || rule.Operator != models.PriorityRuleExclusive {
					continue
				}
				if s.matchesPattern(groupName, rule.Pattern) {
					matchesExclusive = true
					s.LogSchedulerDecision(models.ActionScheduled, "", groupName,
						fmt.Sprintf("Group included by exclusive rule '%s' (pattern: %s)", rule.Name, rule.Pattern),
						rule.Weight, map[string]interface{}{
							"ruleId":   rule.ID,
							"pattern":  rule.Pattern,
							"operator": rule.Operator,
							"weight":   rule.Weight,
						})
					break
				}
			}

			// Skip groups that don't match any exclusive rule (unless they're required)
			if !matchesExclusive && !requiredGroups[groupName] {
				s.LogSchedulerDecision(models.ActionSkipped, "", groupName,
					"Group skipped due to exclusive rules not matching",
					0, map[string]interface{}{
						"reason": "no_exclusive_match",
					})
				continue
			}
		}

		// Include the group
		result = append(result, group)

		// Log inclusion decision if not already logged
		if !requiredGroups[groupName] && (!hasExclusiveRules || !excludedGroups[groupName]) {
			priority := s.calculateGroupPriority(groupName, priorityConfig)
			s.LogSchedulerDecision(models.ActionScheduled, "", groupName,
				fmt.Sprintf("Group included with priority weight %d", priority),
				priority, map[string]interface{}{
					"weight": priority,
				})
		}
	}

	return result
}

// matchesPattern checks if a group name matches a wildcard pattern
func (s *AutoScanService) matchesPattern(groupName, pattern string) bool {
	// Convert wildcard pattern to regex
	// Replace * with .* and escape other regex special characters
	regexPattern := strings.ReplaceAll(regexp.QuoteMeta(pattern), "\\*", ".*")
	regexPattern = "^" + regexPattern + "$"

	matched, err := regexp.MatchString(regexPattern, groupName)
	if err != nil {
		log.Printf("Warning: Invalid pattern '%s': %v", pattern, err)
		return false
	}

	return matched
}

// CancelTask cancels a running or planned auto-scan task
func (s *AutoScanService) CancelTask(taskID string) error {
	s.jobsMutex.Lock()
	defer s.jobsMutex.Unlock()

	// Check if the task exists in active jobs
	job, exists := s.activeJobs[taskID]
	if !exists {
		return fmt.Errorf("auto-scan task with ID %s not found", taskID)
	}

	// Update job status to cancelled
	job.Status = "cancelled"
	now := time.Now()
	job.CompletedAt = &now

	// If the job is currently running, we should also cancel it from the scan queue
	// For now, we'll just mark it as cancelled and let the worker handle it
	log.Printf("Auto-scan task %s cancelled for group %s in repository %s", taskID, job.GroupName, job.RepoID)

	// Save the updated job status to disk
	return s.saveJob(job)
}

// DeleteAllScheduledTasks deletes all pending and scheduled auto-scan tasks
func (s *AutoScanService) DeleteAllScheduledTasks() (int, error) {
	s.jobsMutex.Lock()
	defer s.jobsMutex.Unlock()

	deletedCount := 0
	var errors []string

	// Find all pending tasks
	for taskID, job := range s.activeJobs {
		if job.Status == "pending" {
			// Update job status to cancelled
			job.Status = "cancelled"
			now := time.Now()
			job.CompletedAt = &now
			job.Error = "Bulk deletion requested"

			// Save updated job
			if err := s.saveJob(job); err != nil {
				errors = append(errors, fmt.Sprintf("Failed to save cancelled job %s: %v", taskID, err))
				continue
			}

			// Remove from active jobs
			delete(s.activeJobs, taskID)
			deletedCount++

			log.Printf("Bulk deleted auto-scan task: %s (repo: %s, group: %s)",
				taskID, job.RepoID, job.GroupName)
		}
	}

	// Clear the scan queue of any pending jobs
	queueCleared := 0
	for {
		select {
		case <-s.scanQueue:
			queueCleared++
		default:
			goto queueEmpty
		}
	}
queueEmpty:

	if queueCleared > 0 {
		log.Printf("Cleared %d jobs from scan queue during bulk deletion", queueCleared)
	}

	// Log the bulk deletion operation
	s.LogSchedulerDecision(models.ActionScheduled, "", "",
		fmt.Sprintf("Bulk deleted %d scheduled tasks", deletedCount),
		1, map[string]interface{}{
			"deletedCount": deletedCount,
			"queueCleared": queueCleared,
			"errorCount":   len(errors),
		})

	if len(errors) > 0 {
		return deletedCount, fmt.Errorf("completed with %d errors: %s", len(errors), strings.Join(errors, "; "))
	}

	log.Printf("Successfully bulk deleted %d scheduled auto-scan tasks", deletedCount)
	return deletedCount, nil
}

// continuousQueueManager manages the continuous scheduling queue
// NOTE: This is the CONTINUOUS AUTO-SCHEDULER that uses priority-based task generation.
// This is separate from the regular auto-scan trigger feature which executes pre-scheduled tasks.
func (s *AutoScanService) continuousQueueManager() {
	log.Println("Continuous queue manager started (priority-based scheduling)")

	for {
		select {
		case <-s.queueTicker.C:
			log.Printf("[CONTINUOUS] Ticker fired, continuousScheduling=%v", s.continuousScheduling)
			if s.continuousScheduling {
				s.maintainQueue()
			}
		case <-s.done:
			log.Println("Continuous queue manager stopped")
			return
		}
	}
}

// maintainQueue ensures the queue has enough pending tasks
func (s *AutoScanService) maintainQueue() {
	now := time.Now()
	log.Printf("[CONTINUOUS] maintainQueue called at %v", now.Format("15:04:05"))

	// Count current pending jobs
	pendingCount := s.countPendingJobs()

	if pendingCount < s.targetQueueSize {
		needed := s.targetQueueSize - pendingCount
		log.Printf("Queue has %d pending tasks, target is %d. Adding %d tasks.",
			pendingCount, s.targetQueueSize, needed)

		// Generate new tasks for each enabled configuration
		s.configsMutex.RLock()
		configs := make([]*models.AutoScanConfig, 0, len(s.configs))
		for _, config := range s.configs {
			if config.Enabled && config.IsInTimeWindow(now) {
				configs = append(configs, config)
			}
		}
		s.configsMutex.RUnlock()

		// Distribute tasks across repositories
		if len(configs) == 0 {
			log.Printf("No enabled configurations in time window, skipping task generation")
			return
		}

		tasksPerRepo := needed / len(configs)
		if tasksPerRepo == 0 {
			tasksPerRepo = 1
		}

		for _, config := range configs {
			if needed <= 0 {
				break
			}

			generated := s.generateContinuousTasks(config, tasksPerRepo, now)
			needed -= generated
		}
	}
}

// countPendingJobs counts the number of pending jobs in the queue
func (s *AutoScanService) countPendingJobs() int {
	s.jobsMutex.RLock()
	defer s.jobsMutex.RUnlock()

	count := 0
	for _, job := range s.activeJobs {
		if job.Status == "pending" {
			count++
		}
	}

	return count
}

// generateContinuousTasks generates continuous tasks for a configuration
// NOTE: This function implements PRIORITY-BASED SCHEDULING for the CONTINUOUS AUTO-SCHEDULER only.
// This is different from the regular auto-scan trigger feature, which simply executes
// scheduled tasks according to their configured schedules without priority-based reordering.
func (s *AutoScanService) generateContinuousTasks(config *models.AutoScanConfig, maxTasks int, now time.Time) int {
	if s.dataController == nil {
		return 0
	}

	// Get all groups for the repository
	allGroups, err := s.dataController.GetGroupsForRepo(config.RepoID)
	if err != nil {
		log.Printf("Failed to get groups for repo %s: %v", config.RepoID, err)
		return 0
	}

	// Apply priority-based filtering and sorting (CONTINUOUS SCHEDULER ONLY)
	// The regular auto-scan trigger does NOT use this priority logic - it just executes scheduled tasks
	var groups []models.Group
	if config.PriorityConfig.Enabled {
		groups = s.sortGroupsByPriority(allGroups, config.PriorityConfig)
		log.Printf("Continuous scheduler: Applied priority-based sorting for repo %s (found %d groups)",
			config.RepoID, len(groups))
	} else {
		groups = allGroups
		log.Printf("Continuous scheduler: Using default group order for repo %s (found %d groups)",
			config.RepoID, len(groups))
	}

	// Limit to maxTasks
	if len(groups) > maxTasks {
		groups = groups[:maxTasks]
	}

	// Generate tasks with enhanced load balancing
	generated := 0
	scheduledTime := s.calculateOptimalScheduleTime(config, now)

	for i, group := range groups {
		// Check if we already have a pending task for this group
		if s.hasPendingTask(config.RepoID, group.Groupname) {
			continue
		}

		// Apply intelligent load balancing intervals
		if i > 0 {
			scheduledTime = s.calculateNextScheduleTime(config, scheduledTime, i)
		}

		// Ensure we're still within the time window
		if !config.IsTimeInWindow(scheduledTime) {
			log.Printf("Skipping task generation for %s - scheduled time %v is outside time window",
				group.Groupname, scheduledTime)
			break
		}

		// Create the job
		job := &models.AutoScanJob{
			ID:           uuid.New().String(),
			ConfigID:     config.ID,
			RepoID:       config.RepoID,
			GroupName:    group.Groupname,
			ScheduledFor: scheduledTime,
			Status:       "pending",
			CreatedAt:    now,
		}

		// Add to queue
		select {
		case s.scanQueue <- job:
			s.jobsMutex.Lock()
			s.activeJobs[job.ID] = job
			s.jobsMutex.Unlock()

			s.LogSchedulerDecision(models.ActionScheduled, config.RepoID, group.Groupname,
				"Continuous task generated",
				s.calculateGroupPriority(group.Groupname, config.PriorityConfig),
				map[string]interface{}{
					"configId":     config.ID,
					"scheduledFor": scheduledTime.Format(time.RFC3339),
					"queueType":    "continuous",
				})

			generated++
		default:
			log.Printf("Queue is full, stopping continuous task generation")
			break
		}
	}

	if generated > 0 {
		log.Printf("Generated %d continuous tasks for repo %s", generated, config.RepoID)
	}

	return generated
}

// GetJobByID retrieves a job by its ID
func (s *AutoScanService) GetJobByID(jobID string) (*models.AutoScanJob, error) {
	s.jobsMutex.RLock()
	defer s.jobsMutex.RUnlock()

	job, exists := s.activeJobs[jobID]
	if !exists {
		return nil, fmt.Errorf("job with ID %s not found", jobID)
	}

	return job, nil
}

// UpdateJobStatus updates the status of a job
func (s *AutoScanService) UpdateJobStatus(jobID, status string) error {
	s.jobsMutex.Lock()
	defer s.jobsMutex.Unlock()

	job, exists := s.activeJobs[jobID]
	if !exists {
		return fmt.Errorf("job with ID %s not found", jobID)
	}

	job.Status = status
	now := time.Now()

	switch status {
	case "running":
		job.StartedAt = &now
	case "completed", "failed", "cancelled":
		job.CompletedAt = &now
	}

	// Save the updated job
	if err := s.saveJob(job); err != nil {
		return fmt.Errorf("failed to save job: %v", err)
	}

	log.Printf("Updated job %s status to %s", jobID, status)
	return nil
}

// UpdateJobScanID updates the scan ID for a job (for tracking purposes)
func (s *AutoScanService) UpdateJobScanID(jobID, scanID string) error {
	s.jobsMutex.Lock()
	defer s.jobsMutex.Unlock()

	_, exists := s.activeJobs[jobID]
	if !exists {
		return fmt.Errorf("job with ID %s not found", jobID)
	}

	// Store scan ID to job ID mapping for completion tracking
	s.scanJobMutex.Lock()
	s.scanToJobMap[scanID] = jobID
	s.scanJobMutex.Unlock()

	log.Printf("Job %s is now associated with scan ID %s", jobID, scanID)
	return nil
}

// CompleteJobByScanID marks a job as completed based on its associated scan ID
func (s *AutoScanService) CompleteJobByScanID(scanID string) error {
	s.scanJobMutex.RLock()
	jobID, exists := s.scanToJobMap[scanID]
	s.scanJobMutex.RUnlock()

	if !exists {
		// This is normal - not all scans are from scheduled jobs
		log.Printf("No job found for scan ID %s (scan was not from a scheduled job)", scanID)
		return nil
	}

	// Update job status to completed
	if err := s.UpdateJobStatus(jobID, "completed"); err != nil {
		log.Printf("Failed to mark job %s as completed: %v", jobID, err)
		return err
	}

	// Clean up the mapping
	s.scanJobMutex.Lock()
	delete(s.scanToJobMap, scanID)
	s.scanJobMutex.Unlock()

	log.Printf("Marked job %s as completed (scan ID: %s)", jobID, scanID)
	return nil
}

// RemoveJobFromQueue removes a job from the active jobs queue
func (s *AutoScanService) RemoveJobFromQueue(jobID string) error {
	s.jobsMutex.Lock()
	defer s.jobsMutex.Unlock()

	job, exists := s.activeJobs[jobID]
	if !exists {
		return fmt.Errorf("job with ID %s not found in queue", jobID)
	}

	// Remove from active jobs
	delete(s.activeJobs, jobID)

	log.Printf("Removed job %s (group: %s, repo: %s) from queue", jobID, job.GroupName, job.RepoID)
	return nil
}

// hasPendingTask checks if there's already a pending task for the given group
func (s *AutoScanService) hasPendingTask(repoID, groupName string) bool {
	s.jobsMutex.RLock()
	defer s.jobsMutex.RUnlock()

	for _, job := range s.activeJobs {
		if job.RepoID == repoID && job.GroupName == groupName && job.Status == "pending" {
			return true
		}
	}

	return false
}

// GetContinuousConfig returns the current continuous scheduling configuration
func (s *AutoScanService) GetContinuousConfig() models.ContinuousSchedulingConfig {
	return models.ContinuousSchedulingConfig{
		Enabled:              s.continuousScheduling,
		TargetQueueSize:      s.targetQueueSize,
		CheckIntervalMinutes: 2, // Currently hardcoded to 2 minutes
		MaxConcurrentScans:   s.maxConcurrentScans,
	}
}

// UpdateContinuousConfig updates the continuous scheduling configuration
func (s *AutoScanService) UpdateContinuousConfig(config models.ContinuousSchedulingConfig) error {
	s.continuousScheduling = config.Enabled
	s.targetQueueSize = config.TargetQueueSize
	s.maxConcurrentScans = config.MaxConcurrentScans

	// Update the ticker interval if needed
	if config.CheckIntervalMinutes > 0 && s.queueTicker != nil {
		s.queueTicker.Stop()
		s.queueTicker = time.NewTicker(time.Duration(config.CheckIntervalMinutes) * time.Minute)
	}

	// Save configuration to file for persistence
	if err := s.saveContinuousConfig(config); err != nil {
		log.Printf("Warning: Failed to save continuous scheduling config: %v", err)
	}

	log.Printf("Updated continuous scheduling config: enabled=%v, targetQueueSize=%d, checkInterval=%dm, maxConcurrentScans=%d",
		config.Enabled, config.TargetQueueSize, config.CheckIntervalMinutes, config.MaxConcurrentScans)

	return nil
}

// saveContinuousConfig saves the continuous scheduling configuration to disk
func (s *AutoScanService) saveContinuousConfig(config models.ContinuousSchedulingConfig) error {
	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return err
	}

	// Store in data folder alongside other configuration files
	configPath := filepath.Join("data", "continuous_scheduling.json")
	return os.WriteFile(configPath, data, 0644)
}

// loadContinuousConfig loads the continuous scheduling configuration from disk
func (s *AutoScanService) loadContinuousConfig() models.ContinuousSchedulingConfig {
	// Load from data folder alongside other configuration files
	configPath := filepath.Join("data", "continuous_scheduling.json")

	data, err := os.ReadFile(configPath)
	if err != nil {
		// Return default configuration if file doesn't exist
		log.Printf("No continuous scheduling config file found, using defaults")
		return models.ContinuousSchedulingConfig{
			Enabled:              false, // Disabled by default to prevent excessive logging
			TargetQueueSize:      15,
			CheckIntervalMinutes: 2,
			MaxConcurrentScans:   5, // Default global max concurrent scans
		}
	}

	var config models.ContinuousSchedulingConfig
	if err := json.Unmarshal(data, &config); err != nil {
		log.Printf("Failed to parse continuous scheduling config, using defaults: %v", err)
		return models.ContinuousSchedulingConfig{
			Enabled:              false,
			TargetQueueSize:      15,
			CheckIntervalMinutes: 2,
			MaxConcurrentScans:   5, // Default global max concurrent scans
		}
	}

	return config
}

// calculateOptimalScheduleTime calculates the optimal time to start scheduling tasks
func (s *AutoScanService) calculateOptimalScheduleTime(config *models.AutoScanConfig, now time.Time) time.Time {
	// If we're within the time window, start immediately
	if config.IsInTimeWindow(now) {
		return now
	}

	// If we're outside the time window, calculate the next window start
	nextWindow := config.CalculateNextScanTime(now)
	return nextWindow
}

// calculateNextScheduleTime calculates the next schedule time with load balancing
func (s *AutoScanService) calculateNextScheduleTime(config *models.AutoScanConfig, currentTime time.Time, taskIndex int) time.Time {
	// Apply minimum interval between tasks
	if config.LoadBalancing.MinIntervalBetween != "" {
		if interval, err := time.ParseDuration(config.LoadBalancing.MinIntervalBetween); err == nil {
			return currentTime.Add(interval)
		}
	}

	// Apply intelligent spacing based on MaxScansPerHour
	if config.LoadBalancing.MaxScansPerHour > 0 {
		// Calculate interval to spread scans evenly across the hour
		intervalMinutes := 60.0 / float64(config.LoadBalancing.MaxScansPerHour)
		interval := time.Duration(intervalMinutes * float64(time.Minute))
		return currentTime.Add(interval)
	}

	// Default: 5 minute intervals
	return currentTime.Add(5 * time.Minute)
}
