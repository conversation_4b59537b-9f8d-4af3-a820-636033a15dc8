import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { CompactProgress } from '@/components/ui/enhanced-progress';
import { UnifiedActiveTaskProgress, UnifiedTaskData } from '@/components/common/UnifiedActiveTaskProgress';
import {
  ActiveTaskLoadingSkeleton,
  EmptyActiveTasksState,
  MonitorCardLoadingSkeleton
} from '@/components/common/StandardizedLoadingStates';
import {
  Loader,
  Clock,
  User,
  Database,
  Zap,
  Play,
  Pause,
  X,
  Activity,
  Eye,
  EyeOff,
  RefreshCw,
  AlertCircle
} from 'lucide-react';

import type { ActiveTask } from '@/types/scheduler';
import { Progress as ProgressType, RealTimeProgressUpdate } from '@/types/scanLogs';
import {
  TaskTypeColors,
  formatRelativeTime,
  getTaskTypeDisplayName,
  getProgressPercentage
} from '@/types/scheduler';

interface ActiveTasksMonitorProps {
  tasks: ActiveTask[];
  loading?: boolean;
  onRefresh?: () => void;
  onPauseTask?: (taskId: string) => void;
  onResumeTask?: (taskId: string) => void;
  onCancelTask?: (taskId: string) => void;
  enableRealTimeMonitoring?: boolean;
  // WebSocket connection props (passed from parent)
  isConnected?: boolean;
  isConnecting?: boolean;
  wsError?: string | null;
  lastUpdate?: RealTimeProgressUpdate | null;
  isAdminSubscribed?: boolean;
  realTimeProgress?: Map<string, ProgressType>;
  globalScans?: Map<string, any>;
  onToggleRealTimeMonitoring?: () => void;
  onConnect?: () => void;
}

const ActiveTasksMonitor: React.FC<ActiveTasksMonitorProps> = ({
  tasks,
  loading = false,
  onRefresh,
  onPauseTask,
  onResumeTask,
  onCancelTask,
  enableRealTimeMonitoring = true,
  // WebSocket connection props (from parent)
  isConnected = false,
  isConnecting = false,
  wsError = null,
  lastUpdate = null,
  isAdminSubscribed = false,
  realTimeProgress = new Map(),
  globalScans = new Map(),
  onToggleRealTimeMonitoring,
  onConnect
}) => {
  // Use the real-time monitoring state from props, with fallback to enableRealTimeMonitoring
  const isRealTimeEnabled = enableRealTimeMonitoring;

  // Map real-time progress from parent to task-specific progress
  const getTaskProgress = (task: ActiveTask): ProgressType | undefined => {
    if (!realTimeProgress || !isRealTimeEnabled) return undefined;

    // Debug: Log available progress keys
    if (realTimeProgress.size > 0) {
      console.log(`ActiveTasksMonitor: Available progress keys:`, Array.from(realTimeProgress.keys()));
    }

    // Try to match the task to real-time progress data
    if (task.type === 'usage_scan') {
      // For usage scans, check if we have progress data by task ID first
      const taskProgress = realTimeProgress.get(task.id);
      if (taskProgress) {
        console.log(`ActiveTasksMonitor: Found progress for task ${task.id} by task ID: ${taskProgress.percentage}%`);
        return taskProgress;
      }

      // Check for scan IDs that match this task pattern (GroupName:RepoID:timestamp:active)
      const taskIdPattern = `${task.id}:`;
      for (const [key, progress] of realTimeProgress.entries()) {
        if (key.startsWith(taskIdPattern) && key.includes(':active')) {
          console.log(`ActiveTasksMonitor: Found progress for task ${task.id} by scan ID ${key}: ${progress.percentage}%`);
          return progress;
        }
      }

      // Fallback to scan key format for backward compatibility
      const scanKey = `${task.repository}-${task.groupName}`;
      const scanProgress = realTimeProgress.get(scanKey);
      if (scanProgress) {
        console.log(`ActiveTasksMonitor: Found progress for task ${task.id} by scan key ${scanKey}: ${scanProgress.percentage}%`);
        return scanProgress;
      }

      console.log(`ActiveTasksMonitor: No progress found for usage_scan task ${task.id}, checked keys: ${task.id}, ${scanKey}, and scan ID pattern ${taskIdPattern}*`);
    } else if (task.type === 'scan' || task.type === 'auto-scan') {
      // For other scan types, match by repository and group name
      const scanKey = `${task.repository}-${task.groupName}`;
      const scanProgress = realTimeProgress.get(scanKey);
      if (scanProgress) {
        console.log(`ActiveTasksMonitor: Found progress for task ${task.id} by scan key ${scanKey}: ${scanProgress.percentage}%`);
        return scanProgress;
      }

      console.log(`ActiveTasksMonitor: No progress found for ${task.type} task ${task.id}, checked key: ${scanKey}`);
    }

    return undefined;
  };






  if (loading) {
    return <MonitorCardLoadingSkeleton />;
  }

  // Get active scans from WebSocket that don't have corresponding active tasks
  const activeWebSocketScans = Array.from(globalScans.values()).filter(scan => {
    if (scan.status !== 'running' && scan.status !== 'initializing' && scan.status !== 'running_with_errors') return false;

    // Filter out scans with missing essential data
    if (!scan.groupName || !scan.repoId) {
      console.warn('ActiveTasksMonitor: Filtering out scan with missing data:', scan);
      return false;
    }

    // Check if there's already an active task for this scan
    const hasActiveTask = tasks?.some(task =>
      task.type === 'usage_scan' &&
      task.repository === scan.repoId &&
      task.groupName === scan.groupName &&
      task.status === 'running'
    );

    // Only include scans that don't have corresponding active tasks
    return !hasActiveTask;
  });

  const totalActiveItems = (tasks?.length || 0) + activeWebSocketScans.length;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Zap className="h-5 w-5 text-green-600" />
              <span>Active Tasks & Real-time Scans</span>
              <Badge variant="secondary">{totalActiveItems} active</Badge>
            </CardTitle>
            <CardDescription>
              Scheduler tasks and real-time scan monitoring in one unified view
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            {/* Real-time monitoring toggle */}
            {onToggleRealTimeMonitoring && (
              <Button
                variant="outline"
                size="sm"
                onClick={onToggleRealTimeMonitoring}
                className={isRealTimeEnabled ? 'bg-green-50 border-green-200 text-green-700' : ''}
              >
                <Activity className={`h-4 w-4 mr-2 ${isRealTimeEnabled ? 'text-green-600' : ''}`} />
                {isRealTimeEnabled ? 'Real-time On' : 'Real-time Off'}
              </Button>
            )}

            {/* Refresh button */}
            {onRefresh && (
              <Button
                variant="outline"
                size="sm"
                onClick={onRefresh}
                disabled={loading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                {loading ? 'Refreshing...' : 'Refresh'}
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {totalActiveItems === 0 ? (
          <EmptyActiveTasksState
            title="No Active Tasks or Scans"
            description="All tasks are currently idle. New tasks and scans will appear here when they start running."
            icon={Clock}
          />
        ) : (
          <div className="space-y-4">
            {/* Scheduler Tasks */}
            {(tasks || []).map((task) => (
              <div
                key={task.id}
                className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
              >
                {/* Task Header */}
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <Badge className={`${TaskTypeColors[task.type]} border-0`}>
                      {getTaskTypeDisplayName(task.type)}
                    </Badge>
                    <Badge
                      variant="outline"
                      className={`${task.status === 'running' ? 'border-green-500 text-green-700' : 'border-yellow-500 text-yellow-700'}`}
                    >
                      <Loader className="h-3 w-3 mr-1 animate-spin" />
                      {task.status}
                    </Badge>
                    <h3 className="font-medium">{task.name}</h3>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-500">
                      Started {formatRelativeTime(task.startedAt)}
                    </span>
                    <div className="flex items-center space-x-1">
                      {onPauseTask && task.status === 'running' && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onPauseTask(task.id)}
                          title="Pause Task"
                        >
                          <Pause className="h-4 w-4" />
                        </Button>
                      )}
                      {onResumeTask && task.status !== 'running' && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onResumeTask(task.id)}
                          title="Resume Task"
                        >
                          <Play className="h-4 w-4" />
                        </Button>
                      )}
                      {onCancelTask && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onCancelTask(task.id, task.type)}
                          title="Cancel Task"
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                </div>

                {/* Progress Bar */}
                {(() => {
                  const taskProgress = getTaskProgress(task);
                  return (task.progress || taskProgress) && (
                    <div className="mb-3">
                      {/* Use real-time progress if available, otherwise fall back to task progress */}
                      {taskProgress && isRealTimeEnabled ? (
                        <div className="space-y-2">
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <Activity className="h-3 w-3 text-green-500" />
                            <span>Live Progress</span>
                          </div>
                          <CompactProgress
                            progress={taskProgress}
                            className="mt-1"
                            showCurrentFile={true}
                          />
                        </div>
                      ) : task.progress ? (
                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium text-gray-700">
                              {task.progress.description || 'Processing...'}
                            </span>
                            <span className="text-sm text-gray-500">
                              {task.progress.current} / {task.progress.total} ({getProgressPercentage(task.progress)}%)
                            </span>
                          </div>
                          <Progress
                            value={getProgressPercentage(task.progress)}
                            className="h-2"
                          />
                        </div>
                      ) : null}
                    </div>
                  );
                })()}

                {/* Task Details */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
                  <div className="flex items-center space-x-2">
                    <Database className="h-4 w-4" />
                    <div>
                      <span className="font-medium">Repository:</span>
                      <div>{task.repository || 'N/A'}</div>
                    </div>
                  </div>

                  {task.groupName && (
                    <div className="flex items-center space-x-2">
                      <User className="h-4 w-4" />
                      <div>
                        <span className="font-medium">Group:</span>
                        <div>{task.groupName}</div>
                      </div>
                    </div>
                  )}

                  {task.workerId && (
                    <div className="flex items-center space-x-2">
                      <Zap className="h-4 w-4" />
                      <div>
                        <span className="font-medium">Worker:</span>
                        <div>{task.workerId}</div>
                      </div>
                    </div>
                  )}

                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4" />
                    <div>
                      <span className="font-medium">Started:</span>
                      <div>{new Date(task.startedAt).toLocaleTimeString()}</div>
                    </div>
                  </div>
                </div>

                {/* Metadata */}
                {task.metadata && Object.keys(task.metadata).length > 0 && (
                  <div className="mt-3 pt-3 border-t">
                    <details className="group">
                      <summary className="cursor-pointer text-sm font-medium text-gray-700 hover:text-gray-900">
                        Task Metadata ({Object.keys(task.metadata).length} items)
                      </summary>
                      <div className="mt-2 pl-4 space-y-1">
                        {Object.entries(task.metadata).map(([key, value]) => (
                          <div key={key} className="text-xs text-gray-600">
                            <span className="font-medium">{key}:</span> {JSON.stringify(value)}
                          </div>
                        ))}
                      </div>
                    </details>
                  </div>
                )}
              </div>
            ))}

            {/* Real-time Scans (not tracked by scheduler) */}
            {activeWebSocketScans.length > 0 && (
              <>
                {(tasks?.length || 0) > 0 && (
                  <div className="border-t pt-4 mt-4">
                    <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center gap-2">
                      <Activity className="h-4 w-4 text-blue-500" />
                      Real-time Scans ({activeWebSocketScans.length})
                    </h4>
                  </div>
                )}
                {activeWebSocketScans.map((scan) => (
                  <div
                    key={`${scan.repoId}-${scan.groupName}`}
                    className="border rounded-lg p-4 hover:bg-blue-50/50 transition-colors border-blue-200"
                  >
                    {/* Scan Header */}
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <Badge className="bg-blue-100 text-blue-800 border-blue-200">
                          Live Scan
                        </Badge>
                        {scan.status === 'failed' ? (
                          <Badge variant="outline" className="border-red-500 text-red-700">
                            <AlertCircle className="h-3 w-3 mr-1" />
                            failed
                          </Badge>
                        ) : scan.status === 'initializing' ? (
                          <Badge variant="outline" className="border-blue-500 text-blue-700">
                            <Loader className="h-3 w-3 mr-1 animate-spin" />
                            initializing
                          </Badge>
                        ) : scan.status === 'running_with_errors' || scan.hasConnectionError ? (
                          <Badge variant="outline" className="border-yellow-500 text-yellow-700">
                            <Loader className="h-3 w-3 mr-1 animate-spin" />
                            running (remote failed)
                          </Badge>
                        ) : scan.possibleConnectionFailure ? (
                          <Badge variant="outline" className="border-yellow-500 text-yellow-700">
                            <AlertCircle className="h-3 w-3 mr-1" />
                            connection issue
                          </Badge>
                        ) : (
                          <Badge variant="outline" className="border-green-500 text-green-700">
                            <Loader className="h-3 w-3 mr-1 animate-spin" />
                            running
                          </Badge>
                        )}
                        <h3 className="font-medium">Usage Scan: {scan.groupName}</h3>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-500">
                          Started {Math.floor((Date.now() - scan.startTime) / 60000)} minutes ago
                        </span>
                      </div>
                    </div>

                    {/* Error Message for Failed Scans */}
                    {scan.status === 'failed' && scan.errorMessage && (
                      <div className="mb-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                        <div className="flex items-center gap-2 text-sm text-red-700">
                          <AlertCircle className="h-4 w-4" />
                          <span className="font-medium">Scan Failed</span>
                        </div>
                        <p className="text-sm text-red-600 mt-1">{scan.errorMessage}</p>
                        {scan.possibleConnectionFailure && (
                          <p className="text-xs text-red-500 mt-1">
                            Tip: Check network connectivity or try scanning local repository only
                          </p>
                        )}
                      </div>
                    )}

                    {/* Connection Error Warning (scan continues with local) */}
                    {(scan.status === 'running_with_errors' || scan.hasConnectionError) && scan.errorMessage && (
                      <div className="mb-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <div className="flex items-center gap-2 text-sm text-yellow-700">
                          <AlertCircle className="h-4 w-4" />
                          <span className="font-medium">Remote Connection Failed</span>
                        </div>
                        <p className="text-sm text-yellow-600 mt-1">{scan.errorMessage}</p>
                        <p className="text-xs text-yellow-600 mt-1">
                          <strong>Continuing with local repository scan...</strong>
                        </p>
                      </div>
                    )}

                    {/* Connection Issue Warning */}
                    {scan.possibleConnectionFailure && scan.status !== 'failed' && (
                      <div className="mb-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <div className="flex items-center gap-2 text-sm text-yellow-700">
                          <AlertCircle className="h-4 w-4" />
                          <span className="font-medium">Possible Connection Issue</span>
                        </div>
                        <p className="text-sm text-yellow-600 mt-1">
                          Scan completed quickly with no results. This may indicate a connection failure to the remote repository.
                        </p>
                      </div>
                    )}

                    {/* Live Progress */}
                    {scan.progress && (scan.status === 'running' || scan.status === 'running_with_errors') && (
                      <div className="mb-3">
                        <div className="space-y-2">
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <Activity className={`h-3 w-3 animate-pulse ${scan.status === 'running_with_errors' ? 'text-yellow-500' : 'text-green-500'}`} />
                            <span>{scan.status === 'running_with_errors' ? 'Local Scan Progress' : 'Live Progress'}</span>
                          </div>
                          <CompactProgress
                            progress={scan.progress}
                            className="mt-1"
                            showCurrentFile={true}
                          />
                        </div>
                      </div>
                    )}

                    {/* Scan Details */}
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm text-gray-600">
                      <div className="flex items-center space-x-2">
                        <Database className="h-4 w-4" />
                        <div>
                          <span className="font-medium">Repository:</span>
                          <div>{scan.repoId}</div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        <User className="h-4 w-4" />
                        <div>
                          <span className="font-medium">Group:</span>
                          <div>{scan.groupName}</div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Clock className="h-4 w-4" />
                        <div>
                          <span className="font-medium">Scan ID:</span>
                          <div className="font-mono text-xs">{scan.scanId?.substring(0, 8)}...</div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ActiveTasksMonitor;
