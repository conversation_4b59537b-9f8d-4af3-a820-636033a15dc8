import { useEffect, useState, useCallback } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/components/ui/use-toast"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  RefreshCw,
  Pause,
  AlertCircle,
  CheckCircle,
  Activity,
  Calendar,
  Zap,
  Trash2
} from "lucide-react"
import api from "@/api/client"
import TaskHistoryLogs from "@/components/scheduler/TaskHistoryLogs"
import TaskManagementControls from "@/components/scheduler/TaskManagementControls"
import TasksQueue from "@/components/scheduler/TasksQueue"
import { AutoScanManagement } from "@/components/scheduler/AutoScanManagement"

import { useAdminWebSocket } from "@/hooks/useAdminWebSocket"
import { WebSocketConnectionStatus } from "@/components/common/ConnectionStatusIndicator"

import type {
  SchedulerStatus,
  SchedulerOverview,
  PlannedTask,
  ActiveTask,
  FailedTask,
  CompletedTask,
  ServiceStatus
} from "@/types/scheduler"
import {
  ServiceStatusColors,
  OverallHealthColors,
  formatRelativeTime
} from "@/types/scheduler"
import { Progress as ProgressType } from '@/types/scanLogs'

// Small statistics card component
interface StatisticsCardProps {
  title: string
  value: number
  icon: React.ReactNode
  color: string
}

const StatisticsCard: React.FC<StatisticsCardProps> = ({ title, value, icon, color }) => (
  <Card className="h-16">
    <CardContent className="p-3">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-xs font-medium text-gray-600">{title}</p>
          <p className={`text-lg font-bold ${color}`}>{value}</p>
        </div>
        <div className={color}>
          {icon}
        </div>
      </div>
    </CardContent>
  </Card>
)

const SchedulerDashboard = () => {
  const { toast } = useToast()

  // State management
  const [status, setStatus] = useState<SchedulerStatus | null>(null)
  const [overview, setOverview] = useState<SchedulerOverview | null>(null)
  const [plannedTasks, setPlannedTasks] = useState<PlannedTask[]>([])
  const [activeTasks, setActiveTasks] = useState<ActiveTask[]>([])
  const [failedTasks, setFailedTasks] = useState<FailedTask[]>([])
  const [completedTasks, setCompletedTasks] = useState<CompletedTask[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  // Track recently retried tasks to prevent them from being moved back to failed immediately
  const [recentlyRetriedTasks, setRecentlyRetriedTasks] = useState<Set<string>>(new Set())
  const [error, setError] = useState<string | null>(null)
  const [lastRefreshTime, setLastRefreshTime] = useState<Date | null>(null)
  const [logsExpanded, setLogsExpanded] = useState(false)

  // Real-time monitoring state
  const [isRealTimeEnabled, setIsRealTimeEnabled] = useState(true)
  const [realTimeProgress, setRealTimeProgress] = useState<Map<string, ProgressType>>(new Map())
  const [globalScans, setGlobalScans] = useState<Map<string, any>>(new Map())

  // Event deduplication state
  const [processedEvents, setProcessedEvents] = useState<Set<string>>(new Set())
  const [recentlyCancelledTasks, setRecentlyCancelledTasks] = useState<Set<string>>(new Set())

  // Admin WebSocket for real-time progress monitoring
  const {
    isConnected,
    isConnecting,
    error: wsError,
    lastUpdate,
    isAdminSubscribed,
    subscribeToAdminMonitoring,
    unsubscribeFromAdminMonitoring,
    connect,
    getActiveScans
  } = useAdminWebSocket({
    autoConnect: isRealTimeEnabled
  })

  // Subscribe to admin monitoring when connected and real-time is enabled
  useEffect(() => {
    if (isConnected && isRealTimeEnabled && !isAdminSubscribed) {
      subscribeToAdminMonitoring()
    } else if (isAdminSubscribed && !isRealTimeEnabled) {
      unsubscribeFromAdminMonitoring()
    }
  }, [isConnected, isRealTimeEnabled, isAdminSubscribed, subscribeToAdminMonitoring, unsubscribeFromAdminMonitoring])

  // Handle real-time progress updates and global scan tracking
  useEffect(() => {
    if (!lastUpdate || !isRealTimeEnabled) return

    const update = lastUpdate

    // Track all active scans globally (for comprehensive monitoring)
    if (update.eventType === 'scan_init') {
      // Validate scan data before adding
      if (!update.groupName || !update.repoId) {
        console.warn('SchedulerDashboard: Received scan_init with missing data:', {
          scanId: update.scanId,
          repoId: update.repoId,
          groupName: update.groupName,
          eventType: update.eventType
        });
        return; // Skip adding incomplete scan data
      }

      setGlobalScans(prev => {
        const newScans = new Map(prev)
        const scanKey = `${update.repoId}-${update.groupName}`

        // Check if this scan already exists (might be from a retry)
        const existingScan = newScans.get(scanKey)
        if (existingScan) {
          // Update existing scan with real scan ID and progress
          newScans.set(scanKey, {
            ...existingScan,
            scanId: update.scanId, // Update with real scan ID
            status: 'initializing',
            lastUpdate: new Date(),
            progress: update.progress || existingScan.progress
          })
        } else {
          // Create new scan entry
          newScans.set(scanKey, {
            scanId: update.scanId,
            repoId: update.repoId,
            groupName: update.groupName,
            startTime: Date.now(),
            status: 'initializing',
            lastUpdate: new Date(),
            progress: update.progress || {
              current: 0,
              total: 1,
              percentage: 0,
              description: 'Initializing scan and preparing authentication...'
            }
          })
        }

        return newScans
      })

      // Also update real-time progress for task matching
      setRealTimeProgress(prev => {
        const newProgress = new Map(prev)
        const scanKey = `${update.repoId}-${update.groupName}`

        // Store progress with unique scan ID for proper isolation
        newProgress.set(update.scanId, update.progress || {
          current: 0,
          total: 1,
          percentage: 0,
          description: 'Initializing scan and preparing authentication...'
        })

        // Also store with scan key for backward compatibility
        newProgress.set(scanKey, update.progress || {
          current: 0,
          total: 1,
          percentage: 0,
          description: 'Initializing scan and preparing authentication...'
        })

        // Find matching task and set progress for it too
        const matchingTask = activeTasks?.find(task => {
          if (task.type === 'usage_scan') {
            // Check if scan ID matches the expected pattern for this task
            const taskIdPattern = `${task.id}:`;
            return update.scanId.startsWith(taskIdPattern);
          } else if (task.type === 'scan' || task.type === 'auto-scan') {
            return task.repository === update.repoId && task.groupName === update.groupName;
          }
          return false;
        });

        if (matchingTask) {
          newProgress.set(matchingTask.id, update.progress || {
            current: 0,
            total: 1,
            percentage: 0,
            description: 'Initializing scan and preparing authentication...'
          });
        }

        return newProgress
      })
    }

    if (update.eventType === 'scan_start') {
      // Validate scan data before adding
      if (!update.groupName || !update.repoId) {
        console.warn('SchedulerDashboard: Received scan_start with missing data:', {
          scanId: update.scanId,
          repoId: update.repoId,
          groupName: update.groupName,
          eventType: update.eventType
        });
        return; // Skip adding incomplete scan data
      }

      console.log(`SchedulerDashboard: Scan started for ${update.scanId} (${update.groupName}@${update.repoId})`);

      setGlobalScans(prev => {
        const newScans = new Map(prev)
        const scanKey = `${update.repoId}-${update.groupName}`
        newScans.set(scanKey, {
          scanId: update.scanId,
          repoId: update.repoId,
          groupName: update.groupName,
          startTime: Date.now(),
          status: 'running',
          progress: update.progress
        })
        return newScans
      })
    }

    // Update progress for existing scans
    if ((update.eventType === 'progress' || update.eventType === 'file_progress' || update.eventType === 'scan_init') && update.progress) {
      const scanKey = `${update.repoId}-${update.groupName}`

      // Try to match the scan to an active task
      const matchingTask = activeTasks?.find(task => {
        if (task.type === 'usage_scan') {
          // For usage scans, check if scan ID matches the expected pattern for this task
          const taskIdPattern = `${task.id}:`;
          return update.scanId.startsWith(taskIdPattern);
        } else if (task.type === 'scan' || task.type === 'auto-scan') {
          // For other scan types, match by repository and group name
          return task.repository === update.repoId && task.groupName === update.groupName;
        }
        return false;
      });

      // Update real-time progress map
      setRealTimeProgress(prev => {
        const newProgress = new Map(prev)

        // Store progress with unique scan ID for proper isolation
        newProgress.set(update.scanId, update.progress!)

        // Also store with scan key for backward compatibility
        newProgress.set(scanKey, update.progress!)

        // If we found a matching task, also store with task ID for easy lookup
        if (matchingTask && update.progress) {
          console.log(`SchedulerDashboard: Updating progress for task ${matchingTask.id} (${matchingTask.type}): ${update.progress.percentage}% (scanId: ${update.scanId})`);
          newProgress.set(matchingTask.id, update.progress);
        } else {
          console.log(`SchedulerDashboard: No matching task found for scan update: ${update.groupName} in ${update.repoId} (scanId: ${update.scanId})`);
          console.log(`SchedulerDashboard: Available tasks:`, activeTasks?.map(t => ({ id: t.id, type: t.type, repo: t.repository, group: t.groupName })));
        }

        return newProgress
      })

      // Update global scans with latest progress
      setGlobalScans(prev => {
        const newScans = new Map(prev)
        const existingScan = newScans.get(scanKey)
        if (existingScan) {
          newScans.set(scanKey, {
            ...existingScan,
            progress: update.progress,
            lastUpdate: Date.now()
          })
        }
        return newScans
      })
    }

    // Handle scan completion
    if (update.eventType === 'scan_complete') {
      const scanKey = `${update.repoId}-${update.groupName}`

      // Find matching task for cleanup
      const matchingTask = activeTasks?.find(task => {
        if (task.type === 'usage_scan') {
          const expectedScanId = `${task.id}:active`;
          return update.scanId === expectedScanId;
        } else if (task.type === 'scan' || task.type === 'auto-scan') {
          return task.repository === update.repoId && task.groupName === update.groupName;
        }
        return false;
      });

      // Check if this was a quick completion (potential connection failure)
      const scanStartTime = globalScans.get(scanKey)?.startTime;
      const scanDuration = scanStartTime ? Date.now() - scanStartTime : 0;
      const isQuickCompletion = scanDuration < 10000; // Less than 10 seconds
      const hasNoResults = !update.progress || update.progress.current === 0;

      if (isQuickCompletion && hasNoResults) {
        console.warn(`SchedulerDashboard: Scan ${update.scanId} completed quickly with no results - possible connection failure`);
      }

      // Update global scans status
      setGlobalScans(prev => {
        const newScans = new Map(prev)
        const existingScan = newScans.get(scanKey)
        if (existingScan) {
          newScans.set(scanKey, {
            ...existingScan,
            status: isQuickCompletion && hasNoResults ? 'failed' : 'completed',
            completedAt: Date.now(),
            progress: update.progress,
            possibleConnectionFailure: isQuickCompletion && hasNoResults
          })
        }
        return newScans
      })

      // Keep progress data for a short time after completion
      setTimeout(() => {
        setRealTimeProgress(prev => {
          const newProgress = new Map(prev)
          newProgress.delete(scanKey)

          // Also clean up task-specific progress
          if (matchingTask) {
            console.log(`SchedulerDashboard: Cleaning up progress for completed scan: ${matchingTask.id} (${matchingTask.type})`);
            newProgress.delete(matchingTask.id);
          }

          return newProgress
        })
        setGlobalScans(prev => {
          const newScans = new Map(prev)
          newScans.delete(scanKey)
          return newScans
        })
      }, 30000) // Remove after 30 seconds
    }

    // Handle scan errors (connection failures, auth issues, etc.)
    if (update.eventType === 'scan_error' || update.eventType === 'error') {
      const scanKey = `${update.repoId}-${update.groupName}`

      console.warn(`SchedulerDashboard: Scan error for ${update.scanId}:`, update.error || update.message);

      // Find matching task
      const matchingTask = activeTasks?.find(task => {
        if (task.type === 'usage_scan') {
          const expectedScanId = `${task.id}:active`;
          return update.scanId === expectedScanId;
        } else if (task.type === 'scan' || task.type === 'auto-scan') {
          return task.repository === update.repoId && task.groupName === update.groupName;
        }
        return false;
      });

      // Check if this is a connection error (should continue with local scan)
      const isConnectionError = (update.error || update.message || '').toLowerCase().includes('connect') ||
                               (update.error || update.message || '').toLowerCase().includes('timeout') ||
                               (update.error || update.message || '').toLowerCase().includes('auth');

      // Check if scan is still in initialization phase
      const existingScan = globalScans.get(scanKey);
      const isInitializing = existingScan?.status === 'initializing';

      // Update global scans to show warning but keep running if connection error or still initializing
      setGlobalScans(prev => {
        const newScans = new Map(prev)
        const existingScan = newScans.get(scanKey)
        if (existingScan) {
          newScans.set(scanKey, {
            ...existingScan,
            status: (isConnectionError || isInitializing) ? 'running_with_errors' : 'failed',
            errorMessage: update.error || update.message || 'Scan error occurred',
            hasConnectionError: isConnectionError,
            isInitializing: isInitializing,
            lastErrorTime: Date.now()
          })
        }
        return newScans
      })

      // Only clean up if this is a complete failure (not a connection error or initialization phase that might continue)
      if (!isConnectionError && !isInitializing) {
        setTimeout(() => {
          setRealTimeProgress(prev => {
            const newProgress = new Map(prev)
            newProgress.delete(scanKey)

            if (matchingTask) {
              console.log(`SchedulerDashboard: Cleaning up progress for failed scan: ${matchingTask.id}`);
              newProgress.delete(matchingTask.id);
            }

            return newProgress
          })
          setGlobalScans(prev => {
            const newScans = new Map(prev)
            newScans.delete(scanKey)
            return newScans
          })
        }, 60000) // Keep error visible for 60 seconds
      }
    }
  }, [lastUpdate, isRealTimeEnabled, activeTasks])

  const toggleRealTimeMonitoring = () => {
    setIsRealTimeEnabled(!isRealTimeEnabled)
    if (!isConnected && !isRealTimeEnabled) {
      connect()
    }
  }

  // Restore progress for active scans when component mounts or real-time is enabled
  useEffect(() => {
    if (isRealTimeEnabled && isConnected && isAdminSubscribed && activeTasks && activeTasks.length > 0) {
      console.log('SchedulerDashboard: Checking for active scan tasks to restore progress')

      // Check for active scan tasks and restore their progress, excluding recently cancelled tasks
      const scanTasks = activeTasks.filter(task =>
        task.type === 'usage_scan' &&
        (task.status === 'running' || task.status === 'completing') &&
        !recentlyCancelledTasks.has(task.id)
      )

      if (scanTasks.length > 0) {
        console.log(`SchedulerDashboard: Found ${scanTasks.length} active scan tasks, requesting current progress`)

        // Request current active scans from WebSocket
        const activeScans = getActiveScans()
        if (activeScans && activeScans.length > 0) {
          console.log(`SchedulerDashboard: Received ${activeScans.length} active scans from WebSocket`)

          // Update global scans and progress maps
          activeScans.forEach((scan: any) => {
            const scanKey = `${scan.repoId}-${scan.groupName}`

            // Try to match scan to a task
            const matchingTask = scanTasks.find(task => {
              if (task.type === 'usage_scan') {
                const expectedScanId = `${task.id}:active`;
                return scan.scanId === expectedScanId;
              } else if (task.type === 'scan' || task.type === 'auto-scan') {
                return task.repository === scan.repoId && task.groupName === scan.groupName;
              }
              return false;
            });

            setGlobalScans(prev => {
              const newScans = new Map(prev)
              newScans.set(scanKey, {
                scanId: scan.scanId,
                repoId: scan.repoId,
                groupName: scan.groupName,
                startTime: scan.startTime || Date.now(),
                status: 'running',
                progress: scan.progress
              })
              return newScans
            })

            if (scan.progress) {
              setRealTimeProgress(prev => {
                const newProgress = new Map(prev)
                newProgress.set(scanKey, scan.progress)

                // If we found a matching task, also store with task ID
                if (matchingTask) {
                  console.log(`SchedulerDashboard: Restoring progress for task ${matchingTask.id}: ${scan.progress.percentage}%`);
                  newProgress.set(matchingTask.id, scan.progress);
                }

                return newProgress
              })
            }
          })
        }
      }
    }
  }, [isRealTimeEnabled, isConnected, isAdminSubscribed, activeTasks, getActiveScans, recentlyCancelledTasks])

  // Load all scheduler data
  const loadSchedulerData = useCallback(async (showLoading = false) => {
    try {
      if (showLoading) {
        setLoading(true)
      } else {
        setRefreshing(true)
      }
      setError(null)

      // Load all data in parallel
      const [
        statusResponse,
        overviewResponse,
        plannedWorkResponse,
        activeTasksResponse,
        failedTasksResponse,
        completedTasksResponse
      ] = await Promise.all([
        api.scheduler.getStatus(),
        api.scheduler.getOverview(),
        api.scheduler.getPlannedWork(),
        api.scheduler.getActiveTasks(),
        api.scheduler.getFailedTasks(),
        api.scheduler.getCompletedTasks()
      ])



      setStatus(statusResponse)
      setOverview(overviewResponse)
      setPlannedTasks(plannedWorkResponse.tasks)
      setActiveTasks(activeTasksResponse.tasks)
      // Filter out recently retried tasks from failed tasks to prevent them from reappearing
      const filteredFailedTasks = failedTasksResponse.tasks.filter(task => !recentlyRetriedTasks.has(task.id));
      console.log('SchedulerDashboard: loadSchedulerData filtering failed tasks. Original:', failedTasksResponse.tasks.length, 'Filtered:', filteredFailedTasks.length, 'Recently retried:', Array.from(recentlyRetriedTasks));
      setFailedTasks(filteredFailedTasks)
      setCompletedTasks(completedTasksResponse.tasks)
      setLastRefreshTime(new Date())
    } catch (err) {
      console.error('Failed to load scheduler data:', err)
      setError('Failed to load scheduler data')
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }, [])

  // Initial load
  useEffect(() => {
    loadSchedulerData(true)
  }, [loadSchedulerData])

  // Auto-refresh every 20 seconds for more responsive UI
  useEffect(() => {
    const interval = setInterval(() => {
      loadSchedulerData(false)
    }, 20000)

    return () => clearInterval(interval)
  }, [loadSchedulerData])

  // Additional faster refresh when there are active tasks to keep UI in sync
  useEffect(() => {
    if ((activeTasks?.length || 0) > 0) {
      const fastInterval = setInterval(() => {
        loadSchedulerData(false)
      }, 10000) // Refresh every 10 seconds when there are active tasks

      return () => clearInterval(fastInterval)
    }
  }, [activeTasks?.length, loadSchedulerData])

  // Manual refresh with cache bypass
  const handleRefresh = async () => {
    try {
      setRefreshing(true)
      setError(null)

      // Load all data in parallel with cache bypass
      const [
        statusResponse,
        overviewResponse,
        plannedWorkResponse,
        activeTasksResponse,
        failedTasksResponse
      ] = await Promise.all([
        api.scheduler.refreshStatus(),
        api.scheduler.refreshOverview(),
        api.scheduler.refreshPlannedWork(),
        api.scheduler.refreshActiveTasks(),
        api.scheduler.refreshFailedTasks()
      ])

      setStatus(statusResponse)
      setOverview(overviewResponse)
      setPlannedTasks(plannedWorkResponse.tasks)
      setActiveTasks(activeTasksResponse.tasks)
      // Filter out recently retried tasks from failed tasks to prevent them from reappearing
      setFailedTasks(failedTasksResponse.tasks.filter(task => !recentlyRetriedTasks.has(task.id)))
      setLastRefreshTime(new Date())

      toast({
        title: 'Success',
        description: 'Dashboard data refreshed successfully',
      })
    } catch (err) {
      console.error('Failed to refresh dashboard data:', err)
      toast({
        title: 'Error',
        description: 'Failed to refresh dashboard data',
        variant: 'destructive',
      })
    } finally {
      setRefreshing(false)
    }
  }

  // Handle scheduler events from WebSocket
  const handleSchedulerEvent = useCallback((event: any) => {
    console.log('Scheduler event received:', event);

    // Create unique event key for deduplication
    // For scheduler events, use taskId; for scan events, use scanId/repoId/groupName
    const eventKey = event.taskId
      ? `${event.eventType}_${event.timestamp}_${event.taskId}`
      : `${event.eventType}_${event.timestamp}_${event.scanId || ''}_${event.repoId || ''}_${event.groupName || ''}`;

    // Check if we've already processed this event
    if (processedEvents.has(eventKey)) {
      console.log('Duplicate event ignored:', eventKey);
      return;
    }

    // Add to processed events
    setProcessedEvents(prev => {
      const newSet = new Set(prev);
      newSet.add(eventKey);

      // Clean up old events (keep only last 100 events to prevent memory leaks)
      if (newSet.size > 100) {
        const eventsArray = Array.from(newSet);
        const recentEvents = eventsArray.slice(-50); // Keep only 50 most recent
        return new Set(recentEvents);
      }

      return newSet;
    });

    // Show user feedback for scheduler events
    const getEventMessage = () => {
      switch (event.eventType) {
        case 'task_cancelled':
          return event.success ? 'Task cancelled successfully' : 'Failed to cancel task';
        case 'task_removed':
          return event.success ? 'Task removed successfully' : 'Failed to remove task';
        case 'task_restarted':
          return event.success ? 'Task restarted successfully' : 'Failed to restart task';
        case 'task_triggered':
          return event.success ? 'Task triggered successfully' : 'Failed to trigger task';
        case 'task_paused':
          return event.success ? 'Task paused successfully' : 'Failed to pause task';
        case 'task_resumed':
          return event.success ? 'Task resumed successfully' : 'Failed to resume task';
        default:
          return 'Scheduler updated';
      }
    };

    // Show toast notification for user feedback
    if (event.success !== undefined) {
      toast({
        title: event.success ? 'Success' : 'Error',
        description: event.message || getEventMessage(),
        variant: event.success ? 'default' : 'destructive',
      });
    }

    // Immediate UI updates and trigger refresh based on event type
    switch (event.eventType) {
      case 'task_cancelled':
      case 'task_removed':
        // Immediately remove the task from UI state for instant feedback
        if (event.taskId) {
          setPlannedTasks(prev => prev.filter(task => task.id !== event.taskId));
          setActiveTasks(prev => prev.filter(task => task.id !== event.taskId));
          setFailedTasks(prev => prev.filter(task => task.id !== event.taskId));

          // Track recently cancelled tasks to prevent restoration
          setRecentlyCancelledTasks(prev => new Set(prev).add(event.taskId));

          // Clear the cancelled task from tracking after 5 seconds
          setTimeout(() => {
            setRecentlyCancelledTasks(prev => {
              const newSet = new Set(prev);
              newSet.delete(event.taskId);
              return newSet;
            });
          }, 5000);
        }
        // Also refresh data from backend to ensure consistency
        setTimeout(() => {
          loadSchedulerData(false);
        }, 500); // Small delay to allow backend state to settle
        break;

      case 'task_restarted':
        // Immediately handle task restart - move from failed to active state
        if (event.success && event.taskId) {
          handleTaskRestarted(event.taskId, event.taskType || 'usage_scan');
        }
        // Delay refresh to allow backend to process retry and reach init phase
        setTimeout(() => {
          loadSchedulerData(false);
        }, 2000); // Increased delay to allow backend processing
        break;

      case 'task_triggered':
        // Handle task trigger - refresh data to show updated state
        setTimeout(() => {
          loadSchedulerData(false);
        }, 1500); // Delay to allow backend to process trigger and start scan
        break;

      case 'scheduler_refresh':
      default:
        // General refresh for any other scheduler events
        setTimeout(() => {
          loadSchedulerData(false);
        }, 500);
        break;
    }
  }, [loadSchedulerData, toast, processedEvents]);

  // Handle WebSocket updates for scheduler events
  useEffect(() => {
    if (lastUpdate && isSchedulerEvent(lastUpdate)) {
      handleSchedulerEvent(lastUpdate);
    }
  }, [lastUpdate, handleSchedulerEvent]);

  // Handle task restart - immediately move from failed to active state
  const handleTaskRestarted = (taskId: string, taskType: string) => {
    console.log('SchedulerDashboard: Handling task restart', taskId, taskType);
    console.log('SchedulerDashboard: Current failed tasks:', failedTasks.map(t => ({ id: t.id, name: t.name })));

    // Find the failed task
    const failedTask = failedTasks.find(t => t.id === taskId);
    if (!failedTask) {
      console.log('SchedulerDashboard: Failed task not found for restart', taskId);
      return;
    }

    console.log('SchedulerDashboard: Found failed task for restart:', {
      id: failedTask.id,
      name: failedTask.name,
      repository: failedTask.repository,
      groupName: failedTask.groupName
    });

    // Track this task as recently retried to prevent it from being moved back to failed
    setRecentlyRetriedTasks(prev => {
      const newSet = new Set(prev).add(taskId);
      console.log('SchedulerDashboard: Added to recently retried tasks:', taskId, 'Set now contains:', Array.from(newSet));
      return newSet;
    });

    // Remove from failed tasks
    setFailedTasks(prev => {
      const filtered = prev.filter(t => t.id !== taskId);
      console.log('SchedulerDashboard: Removed from failed tasks. Before:', prev.length, 'After:', filtered.length);
      return filtered;
    });

    // Create a scan entry in globalScans with initializing status
    const scanKey = `${failedTask.repository}-${failedTask.groupName}`;
    setGlobalScans(prev => {
      const newScans = new Map(prev);
      const scanEntry = {
        scanId: `retry-${Date.now()}`, // Temporary scan ID until real one is received
        groupName: failedTask.groupName,
        repoId: failedTask.repository,
        status: 'initializing' as const,
        startTime: Date.now(),
        lastUpdate: new Date(),
        progress: {
          current: 0,
          total: 1,
          percentage: 0,
          description: 'Retrying scan - initializing...',
          currentFile: failedTask.groupName
        }
      };
      newScans.set(scanKey, scanEntry);
      console.log('SchedulerDashboard: Added scan entry to globalScans:', scanKey, scanEntry);
      return newScans;
    });

    // Clear the recently retried flag after some time to allow normal processing
    setTimeout(() => {
      setRecentlyRetriedTasks(prev => {
        const newSet = new Set(prev);
        newSet.delete(taskId);
        console.log('SchedulerDashboard: Cleared recently retried flag for:', taskId);
        return newSet;
      });
    }, 10000); // Clear after 10 seconds

    console.log('SchedulerDashboard: Moved retried task to active state', scanKey);
  };

  // Helper function to detect scheduler events
  const isSchedulerEvent = (update: any): boolean => {
    const schedulerEventTypes = [
      'task_cancelled',
      'task_restarted',
      'task_removed',
      'task_paused',
      'task_resumed',
      'scheduler_refresh'
    ];
    return schedulerEventTypes.includes(update.eventType);
  };

  // Refresh only active tasks
  const handleActiveTasksRefresh = async () => {
    try {
      const activeTasksResponse = await api.scheduler.refreshActiveTasks()
      setActiveTasks(activeTasksResponse.tasks)
      setLastRefreshTime(new Date())
      toast({
        title: 'Success',
        description: 'Active tasks refreshed successfully',
      })
    } catch (err) {
      console.error('Failed to refresh active tasks:', err)
      toast({
        title: 'Error',
        description: 'Failed to refresh active tasks',
        variant: 'destructive',
      })
    }
  }



  // Map task type to service for API calls
  const getServiceForTaskType = (taskType: string): string => {
    switch (taskType) {
      case 'report':
        return 'report'
      case 'scan':
        return 'usage'
      case 'auto-scan':
        return 'auto-scan'
      default:
        return 'all'
    }
  }

  // Retry failed task
  const handleRetryTask = async (taskId: string) => {
    try {
      console.log('SchedulerDashboard: Retrying task', taskId)

      // Find the task to get its type
      const task = failedTasks.find(t => t.id === taskId)
      if (!task) {
        toast({
          title: 'Error',
          description: 'Task not found',
          variant: 'destructive',
        })
        return
      }

      const service = getServiceForTaskType(task.type)

      // Use the working restart-scan endpoint instead of the unimplemented retry endpoint
      const response = await api.scheduler.restartSpecificScan({
        action: 'retry',
        service: service as any,
        taskId: taskId,
        taskType: task.type,
      })

      if (response.success) {
        console.log('SchedulerDashboard: Task retry initiated successfully, updating UI')

        // Immediate optimistic update - remove from failed tasks
        setFailedTasks(prev => prev.filter(t => t.id !== taskId))

        toast({
          title: 'Success',
          description: response.message || `Task ${task.name} retry initiated - moving to active tasks`,
        })

        // Refresh data after successful retry
        setTimeout(() => loadSchedulerData(false), 1000)
      } else {
        // Handle specific error cases
        if (response.message?.includes('not found')) {
          // Task was already processed or removed
          toast({
            title: 'Task No Longer Available',
            description: 'This failed task may have already been processed or removed. Refreshing the task list...',
            variant: 'destructive',
          })

          // Remove from UI and refresh
          setFailedTasks(prev => prev.filter(t => t.id !== taskId))
          setTimeout(() => loadSchedulerData(false), 500)
        } else {
          toast({
            title: 'Error',
            description: response.message || 'Failed to retry task',
            variant: 'destructive',
          })
        }
      }
    } catch (err) {
      console.error('Error retrying task:', err)
      toast({
        title: 'Error',
        description: 'Failed to retry task',
        variant: 'destructive',
      })
    }
  }

  // Delete failed task
  const handleDeleteTask = async (taskId: string) => {
    try {
      console.log('SchedulerDashboard: Deleting task', taskId)

      // Find the task to get its type
      const task = failedTasks.find(t => t.id === taskId)
      if (!task) {
        toast({
          title: 'Error',
          description: 'Task not found',
          variant: 'destructive',
        })
        return
      }

      // Immediate optimistic update - remove from failed tasks
      setFailedTasks(prev => prev.filter(t => t.id !== taskId))

      toast({
        title: 'Success',
        description: 'Task removed from failed tasks list',
      })

      // Refresh data to ensure consistency
      setTimeout(() => loadSchedulerData(false), 500)
    } catch (err) {
      console.error('Error deleting task:', err)
      toast({
        title: 'Error',
        description: 'Failed to delete task',
        variant: 'destructive',
      })
    }
  }

  // Handle task cancellation
  const handleCancelTask = async (taskId: string, taskType?: string) => {
    try {
      console.log('SchedulerDashboard: Cancelling task', taskId, 'type:', taskType)

      const response = await api.scheduler.cancelTask({
        action: 'cancel',
        service: 'all', // Default to all services for cancel operations
        taskId: taskId,
        taskType: taskType,
      })

      if (response.success) {
        console.log('SchedulerDashboard: Task cancelled successfully, updating UI')

        // Immediate optimistic update - remove task from all lists
        setPlannedTasks(prev => prev.filter(task => task.id !== taskId))
        setActiveTasks(prev => prev.filter(task => task.id !== taskId))
        setFailedTasks(prev => prev.filter(task => task.id !== taskId))

        toast({
          title: 'Success',
          description: response.message || 'Task cancelled successfully',
        })

        // Refresh data to ensure consistency
        setTimeout(() => loadSchedulerData(false), 500)
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Failed to cancel task',
          variant: 'destructive',
        })
      }
    } catch (err) {
      console.error('Failed to cancel task:', err)
      toast({
        title: 'Error',
        description: 'Failed to cancel task',
        variant: 'destructive',
      })
    }
  }

  // Clean up stale tasks
  const handleCleanupStaleTasks = async () => {
    try {
      const response = await api.scheduler.cleanupStaleTasks()

      if (response.success) {
        toast({
          title: 'Success',
          description: response.message || `Cleaned up ${response.totalCleaned} stale tasks`,
        })
        // Refresh active tasks to reflect the changes
        handleActiveTasksRefresh()
      } else {
        toast({
          title: 'Warning',
          description: response.message || 'Cleanup completed with some issues',
          variant: 'destructive',
        })
      }
    } catch (err) {
      console.error('Failed to cleanup stale tasks:', err)
      toast({
        title: 'Error',
        description: 'Failed to cleanup stale tasks',
        variant: 'destructive',
      })
    }
  }

  // Delete all scheduled tasks
  const handleDeleteAllScheduledTasks = async () => {
    // Show confirmation dialog
    const confirmed = window.confirm(
      'Are you sure you want to delete ALL scheduled tasks? This action cannot be undone.\n\n' +
      'This will cancel all pending auto-scan tasks but will not affect currently running scans.'
    )

    if (!confirmed) {
      return
    }

    try {
      const response = await api.scheduler.deleteAllScheduledTasks()

      if (response.success) {
        toast({
          title: 'Success',
          description: response.message || 'All scheduled tasks have been deleted',
        })
        // Refresh the data to show updated task counts
        await loadSchedulerData(false)
      } else {
        toast({
          title: 'Warning',
          description: response.message || 'Deletion completed with some issues',
          variant: 'destructive',
        })
      }
    } catch (err) {
      console.error('Failed to delete all scheduled tasks:', err)
      toast({
        title: 'Error',
        description: 'Failed to delete all scheduled tasks',
        variant: 'destructive',
      })
    }
  }

  // Enhanced planned task management handlers
  const handleTriggerTask = async (taskId: string) => {
    try {
      console.log('SchedulerDashboard: Triggering task', taskId)

      // Use the proper trigger task API for scheduled tasks
      const response = await api.scheduler.triggerTask({
        action: 'trigger',
        service: 'auto-scan', // Scheduled tasks are auto-scan tasks
        taskId: taskId,
      })

      if (response.success) {
        console.log('SchedulerDashboard: Task triggered successfully, refreshing data')

        toast({
          title: 'Success',
          description: response.message || 'Task triggered successfully',
        })

        // Don't do optimistic update - let the backend handle the state transition
        // The task will move to active when the scan actually starts
        // Refresh data to get updated task states with a delay to allow backend processing
        setTimeout(() => {
          loadSchedulerData(false)
        }, 1500)
      } else {
        // Handle specific error cases
        if (response.message?.includes('not found')) {
          // Task was already processed or removed
          toast({
            title: 'Task No Longer Available',
            description: 'This task may have already been processed or removed. Refreshing the task list...',
            variant: 'destructive',
          })

          // Remove from UI and refresh
          setPlannedTasks(prev => prev.filter(task => task.id !== taskId))
          setTimeout(() => loadSchedulerData(false), 500)
        } else {
          toast({
            title: 'Error',
            description: response.message || 'Failed to trigger task',
            variant: 'destructive',
          })
        }
      }
    } catch (err) {
      console.error('Failed to trigger task:', err)
      toast({
        title: 'Error',
        description: 'Failed to trigger task',
        variant: 'destructive',
      })
    }
  }

  const handleDelayTask = async (taskId: string, delayMinutes: number) => {
    try {
      // For now, show a placeholder message since delay API needs to be implemented
      toast({
        title: 'Info',
        description: `Task delay functionality will be implemented. Would delay by ${delayMinutes} minutes.`,
      })
      // TODO: Implement delay API endpoint
      // await api.scheduler.delayTask(taskId, delayMinutes)
      loadSchedulerData(false)
    } catch (err) {
      console.error('Failed to delay task:', err)
      toast({
        title: 'Error',
        description: 'Failed to delay task',
        variant: 'destructive',
      })
    }
  }

  const handlePauseTask = async (taskId: string, taskType?: string) => {
    try {
      // Use existing cancel task API for pausing tasks
      const response = await api.scheduler.cancelTask({
        action: 'pause',
        service: 'all',
        taskId: taskId,
        taskType: taskType,
      })

      if (response.success) {
        toast({
          title: 'Success',
          description: response.message || 'Task paused successfully',
        })
        loadSchedulerData(false)
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Failed to pause task',
          variant: 'destructive',
        })
      }
    } catch (err) {
      console.error('Failed to pause task:', err)
      toast({
        title: 'Error',
        description: 'Failed to pause task',
        variant: 'destructive',
      })
    }
  }

  const handleResumeTask = async (taskId: string, taskType?: string) => {
    try {
      // Use resume scheduler API for resuming tasks
      const response = await api.scheduler.resumeScheduler({
        action: 'resume',
        service: 'all',
        taskId: taskId,
        taskType: taskType,
      })

      if (response.success) {
        toast({
          title: 'Success',
          description: response.message || 'Task resumed successfully',
        })
        loadSchedulerData(false)
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Failed to resume task',
          variant: 'destructive',
        })
      }
    } catch (err) {
      console.error('Failed to resume task:', err)
      toast({
        title: 'Error',
        description: 'Failed to resume task',
        variant: 'destructive',
      })
    }
  }

  const handleReorderTasks = async (taskIds: string[]) => {
    try {
      // For now, show a placeholder message since reorder task API needs to be implemented
      toast({
        title: 'Info',
        description: 'Task reordering functionality will be implemented based on backend support.',
      })
      // TODO: Implement reorder task API endpoint
      // await api.scheduler.reorderTasks(taskIds)
      loadSchedulerData(false)
    } catch (err) {
      console.error('Failed to reorder tasks:', err)
      toast({
        title: 'Error',
        description: 'Failed to reorder tasks',
        variant: 'destructive',
      })
    }
  }

  const handleEditTask = async (taskId: string, updates: any) => {
    try {
      // For now, show a placeholder message since update task API needs to be implemented
      toast({
        title: 'Info',
        description: 'Task editing functionality will be implemented based on backend support.',
      })
      // TODO: Implement update task API endpoint
      // await api.scheduler.updateTask(taskId, updates)
      loadSchedulerData(false)
    } catch (err) {
      console.error('Failed to update task:', err)
      toast({
        title: 'Error',
        description: 'Failed to update task',
        variant: 'destructive',
      })
    }
  }

  // Service status card component
  const ServiceStatusCard = ({ service }: { service: ServiceStatus }) => (
    <Card className="h-16">
      <CardContent className="p-3">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-xs font-medium text-gray-600">{service.name}</p>
            <div className="flex items-center space-x-1 mt-0.5">
              <Badge className={`${ServiceStatusColors[service.status]} border-0 text-xs px-1 py-0`}>
                {service.status}
              </Badge>
              {service.isRunning && (
                <span className="text-xs text-green-600">Running</span>
              )}
            </div>
          </div>
          <div className="h-4 w-4 flex items-center justify-center">
            {service.status === 'running' ? (
              <CheckCircle className="h-4 w-4 text-green-600" />
            ) : service.status === 'error' ? (
              <AlertCircle className="h-4 w-4 text-red-600" />
            ) : (
              <Pause className="h-4 w-4 text-yellow-600" />
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold">Scheduler Dashboard</h1>
        </div>
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin text-blue-600" />
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold">Scheduler Dashboard</h1>
        </div>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2 text-red-600">
              <AlertCircle className="h-5 w-5" />
              <span>{error}</span>
            </div>
            <Button
              onClick={handleRefresh}
              className="mt-4"
              disabled={loading || refreshing}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${(loading || refreshing) ? 'animate-spin' : ''}`} />
              {(loading || refreshing) ? 'Retrying...' : 'Retry'}
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Scheduler Dashboard</h1>
        <div className="flex items-center space-x-4">
          {/* Real-time monitoring connection status */}
          <WebSocketConnectionStatus
            isConnected={isConnected}
            isConnecting={isConnecting}
            error={wsError}
            onConnect={() => {
              setIsRealTimeEnabled(true)
              connect()
            }}
            onDisconnect={() => setIsRealTimeEnabled(false)}
          />

          <div className="flex items-center space-x-2">
            {lastRefreshTime && (
              <span className="text-sm text-gray-500">
                Last updated: {formatRelativeTime(lastRefreshTime.toISOString())}
              </span>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={refreshing}
            >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={handleCleanupStaleTasks}
            className="text-orange-600 border-orange-300 hover:bg-orange-50"
          >
            <AlertCircle className="h-4 w-4 mr-2" />
            Cleanup Stale
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={handleDeleteAllScheduledTasks}
            className="text-red-600 border-red-300 hover:bg-red-50"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete All Scheduled
          </Button>
          </div>
        </div>
      </div>

      {/* Overall Health Status & Statistics */}
      {status && overview && (
        <Card>
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center space-x-2">
              <Activity className="h-5 w-5" />
              <span>System Overview</span>
              <Badge className={`${OverallHealthColors[status.overallHealth]} border-0 ml-2`}>
                {status.overallHealth}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-4 gap-3">
              <ServiceStatusCard service={status.reportScheduler} />
              <ServiceStatusCard service={status.usageScanner} />
              <ServiceStatusCard service={status.autoScanService} />
              <ServiceStatusCard service={status.workerPool} />
              <StatisticsCard
                title="Planned"
                value={overview.totalPlannedTasks}
                icon={<Calendar className="h-4 w-4" />}
                color="text-blue-600"
              />
              <StatisticsCard
                title="Active"
                value={overview.activeTasks}
                icon={<Zap className="h-4 w-4" />}
                color="text-green-600"
              />
              <StatisticsCard
                title="Failed"
                value={overview.failedTasks}
                icon={<AlertCircle className="h-4 w-4" />}
                color="text-red-600"
              />
              <StatisticsCard
                title="Completed today"
                value={overview.completedToday}
                icon={<CheckCircle className="h-4 w-4" />}
                color="text-purple-600"
              />
            </div>
          </CardContent>
        </Card>
      )}



      {/* Main Content Tabs */}
      <Tabs defaultValue="queue" className="space-y-4">
        <TabsList>
          <TabsTrigger value="queue">
            Tasks Queue ({(plannedTasks?.length || 0) + (activeTasks?.length || 0) + (failedTasks?.length || 0)} pending)
          </TabsTrigger>
          <TabsTrigger value="logs">Task History & Logs</TabsTrigger>
          <TabsTrigger value="controls">Management Controls</TabsTrigger>
          <TabsTrigger value="auto-scan-config">Scan Auto-Scheduler Configuration</TabsTrigger>
        </TabsList>

        {/* Tasks Queue Tab - Unified View */}
        <TabsContent value="queue">
          <TasksQueue
            plannedTasks={plannedTasks}
            activeTasks={activeTasks}
            failedTasks={failedTasks}
            completedTasks={completedTasks}
            loading={loading}
            // WebSocket data for real-time progress
            isConnected={isConnected}
            isConnecting={isConnecting}
            wsError={wsError}
            lastUpdate={lastUpdate}
            isAdminSubscribed={isAdminSubscribed}
            realTimeProgress={realTimeProgress}
            globalScans={globalScans}
            // Event handlers
            onRefresh={() => loadSchedulerData(false)}
            onTriggerTask={handleTriggerTask}
            onDelayTask={handleDelayTask}
            onPauseTask={handlePauseTask}
            onResumeTask={handleResumeTask}
            onCancelTask={handleCancelTask}
            onRetryTask={handleRetryTask}
            onDeleteTask={handleDeleteTask}
            onEditTask={handleEditTask}
            onReorderTasks={handleReorderTasks}
            // Real-time monitoring controls
            enableRealTimeMonitoring={isRealTimeEnabled}
            onToggleRealTimeMonitoring={toggleRealTimeMonitoring}
            onConnect={connect}
            // Retry fix - pass the retry tracking state and handlers
            recentlyRetriedTasks={recentlyRetriedTasks}
            onTaskRestarted={handleTaskRestarted}
          />
        </TabsContent>







        {/* Task History & Logs Tab - Integrated scan logs functionality */}
        <TabsContent value="logs">
          <TaskHistoryLogs
            isExpanded={logsExpanded}
            onToggleExpanded={() => setLogsExpanded(!logsExpanded)}
          />
        </TabsContent>



        {/* Management Controls Tab */}
        <TabsContent value="controls">
          <TaskManagementControls
            schedulerStatus={status || undefined}
            onStatusUpdate={() => loadSchedulerData(false)}
          />
        </TabsContent>

        {/* Auto-Scan Configuration Tab */}
        <TabsContent value="auto-scan-config">
          <AutoScanManagement />
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default SchedulerDashboard
